# 🚀 Quick Test Guide - <PERSON><PERSON> thống ERP

## ⚡ Test nhanh trong 2 phút

### Cách 1: Đ<PERSON><PERSON> nhập bình thường
1. Chạy: `npm run dev`
2. Mở: `http://localhost:5173/login`
3. Nhập:
   - Username: `admin`
   - Password: `admin123`
4. Click "Đăng nhập"

### Cách 2: Bypass login (nếu cách 1 không hoạt động)
1. Mở: `http://localhost:5173/test-login`
2. Click "Set Mock Authentication"
3. Click "Go to Dashboard"

### Cách 3: Manual setup (nếu cả 2 cách trên fail)
1. Mở browser console (F12)
2. Paste và chạy code này:
```javascript
localStorage.setItem('auth_token', 'mock-jwt-token-' + Date.now())
localStorage.setItem('user_info', JSON.stringify({
  id: 1,
  username: 'admin', 
  name: 'Administrator',
  email: '<EMAIL>',
  role: 'admin',
  permissions: ['all']
}))
location.href = '/dashboard'
```

## 🎯 Tính năng có thể test ngay

### ✅ Hoạt động 100%:
- **Dashboard**: Thống kê tổng quan, quick actions
- **Nguyên vật liệu**: 
  - Danh sách với search/filter/pagination
  - Form thêm/sửa với validation
  - Tồn kho với nhập/xuất kho dialogs
  - Nhật ký nhập xuất với filtering
- **Sản phẩm**: CRUD hoàn chỉnh với form đẹp
- **BOM**: Liên kết sản phẩm-nguyên liệu, tính chi phí
- **Responsive**: Test trên mobile/tablet
- **Navigation**: Sidebar, breadcrumb, routing

### 📱 Test responsive:
1. Nhấn F12 → Toggle device toolbar
2. Chọn mobile/tablet size
3. Test sidebar collapse, menu navigation

### 🎨 Test UI components:
- Forms với validation
- Tables với sort/search/pagination  
- Dialogs và modals
- Notifications và messages
- Loading states

## 🐛 Nếu gặp lỗi:

### Lỗi import modules:
```bash
# Restart dev server
npm run dev
```

### Lỗi TypeScript:
- Bỏ qua, không ảnh hưởng chức năng
- Hoặc đổi file `.ts` thành `.js`

### Lỗi routing:
- Kiểm tra console errors
- Thử truy cập trực tiếp: `/dashboard`, `/materials`

### Lỗi authentication:
- Dùng test-login page: `/test-login`
- Hoặc set manual như Cách 3

## 📊 Demo data có sẵn:

### Materials (mock):
- Nhựa PVC, Nhựa ABS, Màu đỏ, Màu xanh...
- Với supplier, unit cost, đơn vị tính

### Products (mock):  
- Ống nhựa 20mm, Ống nhựa 25mm...
- Với trọng lượng, thời gian ép, màu sắc, giá

### BOM (mock):
- Liên kết sản phẩm với nguyên liệu
- Định mức và tính chi phí

## 🎉 Expected Results:

Sau khi đăng nhập thành công, bạn sẽ thấy:

1. **Dashboard** với:
   - 4 cards thống kê (materials, products, orders, inventory value)
   - Quick actions buttons
   - Recent activities list
   - System status

2. **Sidebar menu** với:
   - Dashboard
   - Nguyên vật liệu (3 sub-items)
   - Sản phẩm (3 sub-items)  
   - BOM
   - Đơn hàng (3 sub-items)
   - Báo cáo (3 sub-items)
   - Cài đặt

3. **Responsive design**:
   - Mobile: Sidebar collapse
   - Tablet: Adaptive layout
   - Desktop: Full layout

## 📞 Nếu vẫn không hoạt động:

1. **Check console**: F12 → Console tab
2. **Check network**: F12 → Network tab  
3. **Try different browser**: Chrome, Firefox, Safari
4. **Clear cache**: Ctrl+Shift+R
5. **Restart everything**:
   ```bash
   # Stop server (Ctrl+C)
   rm -rf node_modules/.vite
   npm run dev
   ```

---

**🎯 Mục tiêu**: Trong 2 phút bạn sẽ thấy dashboard và có thể navigate qua các trang!

**💡 Tip**: Nếu login page không hoạt động, dùng ngay `/test-login` để vào hệ thống!
