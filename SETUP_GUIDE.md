# Hướng dẫn Setup và Chạy Hệ thống ERP

## 🎯 Tổng quan hoàn thành

Hệ thống ERP quản lý quy trình sản xuất đã được phát triển hoàn chỉnh với các tính năng chính:

### ✅ Đã hoàn thành:
- **Layout và Navigation**: Header, Sidebar responsive với menu đầy đủ
- **Authentication**: Login page với demo account, route guards, JWT handling
- **Module Nguyên vật liệu**: CRUD hoàn chỉnh, tồn kho, nhật ký nhập xuất
- **Module Sản phẩm**: Quản lý sản phẩm với thông tin chi tiết
- **Module BOM**: <PERSON><PERSON><PERSON> mức nguyên vật liệu cho sản phẩm
- **Module Đơn hàng**: <PERSON><PERSON><PERSON> tr<PERSON><PERSON> c<PERSON> bả<PERSON> (sẵn sàng mở rộng)
- **B<PERSON>o cáo**: Template b<PERSON>o c<PERSON>o với thống kê
- **Cài đặt hệ thống**: <PERSON><PERSON><PERSON> hình đầy đủ
- **API Services**: Layer tích hợp backend hoàn chỉnh
- **Utilities**: Helper functions và constants
- **Responsive Design**: Tương thích mobile/tablet

## 🚀 Cách chạy hệ thống

### 1. Cài đặt dependencies
```bash
npm install
```

### 2. Chạy development server
```bash
npm run dev
```

### 3. Truy cập ứng dụng
- URL: `http://localhost:5173`
- Login page: `http://localhost:5173/login`

### 4. Đăng nhập demo
- **Username**: `admin`
- **Password**: `admin123`

## 📱 Tính năng chính có thể test

### 1. Authentication
- ✅ Login/logout hoạt động
- ✅ Route guards bảo vệ các trang
- ✅ Auto redirect khi chưa đăng nhập

### 2. Dashboard
- ✅ Thống kê tổng quan
- ✅ Thao tác nhanh
- ✅ Hoạt động gần đây

### 3. Quản lý Nguyên vật liệu
- ✅ Danh sách với tìm kiếm, lọc, phân trang
- ✅ Form thêm/sửa với validation
- ✅ Tồn kho với nhập/xuất kho
- ✅ Nhật ký nhập xuất với lọc

### 4. Quản lý Sản phẩm
- ✅ CRUD sản phẩm hoàn chỉnh
- ✅ Form với màu sắc, trọng lượng, thời gian ép
- ✅ Validation đầy đủ

### 5. Quản lý BOM
- ✅ Liên kết sản phẩm - nguyên vật liệu
- ✅ Tính toán chi phí nguyên liệu
- ✅ Form với dropdown search

### 6. Responsive Design
- ✅ Mobile-friendly sidebar
- ✅ Adaptive tables
- ✅ Touch-friendly buttons

## 🎨 Theme và Styling

### Màu sắc chủ đạo
- **Primary**: #e7ab3c (vàng cam)
- **Text**: #252525 (đen)
- **Background**: #ffffff (trắng)
- **Success**: #67c23a
- **Warning**: #e6a23c
- **Danger**: #f56c6c

### Typography
- Font chính: System fonts
- Tiếng Việt được hỗ trợ đầy đủ
- Icons: Element Plus Icons

## 🔧 Cấu hình Backend (Mock)

Hiện tại hệ thống sử dụng mock data và localStorage. Để tích hợp backend thật:

### 1. Cập nhật API base URL
File: `src/services/api.js`
```javascript
const API_BASE_URL = 'http://your-backend-url/api'
```

### 2. Backend endpoints cần implement
```
POST /auth/login
POST /auth/refresh
GET  /materials
POST /materials
PUT  /materials/:id
DELETE /materials/:id
GET  /inventories/material
POST /movements
GET  /products
POST /products
PUT  /products/:id
DELETE /products/:id
GET  /product-bom
POST /product-bom
PUT  /product-bom/:id
DELETE /product-bom/:id
```

### 3. Response format mong đợi
```json
{
  "success": true,
  "data": {...},
  "message": "Success message"
}
```

## 📊 Database Schema

### Bảng chính cần tạo:
```sql
-- Nguyên vật liệu
CREATE TABLE materials (
  material_id INT PRIMARY KEY AUTO_INCREMENT,
  material_name VARCHAR(100) NOT NULL,
  supplier VARCHAR(100) NOT NULL,
  unit_cost DECIMAL(10,2) NOT NULL,
  unit VARCHAR(20) NOT NULL
);

-- Sản phẩm
CREATE TABLE products (
  product_id INT PRIMARY KEY AUTO_INCREMENT,
  product_name VARCHAR(100) NOT NULL,
  product_weight_g DECIMAL(8,2) NOT NULL,
  injection_time_s INT NOT NULL,
  color VARCHAR(50) NOT NULL,
  unit_price DECIMAL(10,2) NOT NULL
);

-- BOM
CREATE TABLE product_bom (
  bom_id INT PRIMARY KEY AUTO_INCREMENT,
  product_id INT NOT NULL,
  material_id INT NOT NULL,
  m_quantity DECIMAL(10,3) NOT NULL,
  FOREIGN KEY (product_id) REFERENCES products(product_id),
  FOREIGN KEY (material_id) REFERENCES materials(material_id)
);

-- Tồn kho nguyên vật liệu
CREATE TABLE material_inventory (
  inventory_id INT PRIMARY KEY AUTO_INCREMENT,
  material_id INT NOT NULL,
  received_qty DECIMAL(10,3) NOT NULL,
  current_qty DECIMAL(10,3) NOT NULL,
  in_date DATETIME NOT NULL,
  FOREIGN KEY (material_id) REFERENCES materials(material_id)
);

-- Nhật ký nhập xuất
CREATE TABLE material_movements (
  movement_id INT PRIMARY KEY AUTO_INCREMENT,
  material_id INT NOT NULL,
  movement_type ENUM('IN', 'OUT') NOT NULL,
  quantity DECIMAL(10,3) NOT NULL,
  movement_date DATETIME NOT NULL,
  remark TEXT,
  FOREIGN KEY (material_id) REFERENCES materials(material_id)
);
```

## 🐛 Troubleshooting

### Lỗi thường gặp:

1. **Port 5173 đã được sử dụng**
   ```bash
   npm run dev -- --port 3000
   ```

2. **Module not found**
   - Restart dev server
   - Clear cache: `rm -rf node_modules/.vite`

3. **TypeScript errors**
   - Các file .vue đã được tạo, TypeScript sẽ tự detect

## 🚀 Deployment

### Build production
```bash
npm run build
```

### Preview build
```bash
npm run preview
```

### Docker deployment
```bash
docker build -t erp-system .
docker run -p 3000:3000 erp-system
```

## 📈 Mở rộng tương lai

### Tính năng có thể thêm:
- [ ] Charts và biểu đồ thống kê
- [ ] Export Excel/PDF
- [ ] Real-time notifications
- [ ] Multi-language support
- [ ] Advanced reporting
- [ ] Workflow management
- [ ] Mobile app

### Technical improvements:
- [ ] Unit tests với Vitest
- [ ] E2E tests với Playwright
- [ ] State management với Pinia
- [ ] PWA support
- [ ] Performance optimization

## 📞 Support

Nếu gặp vấn đề:
1. Kiểm tra console browser
2. Xem network tab cho API errors
3. Restart dev server
4. Clear browser cache

---

**🎉 Hệ thống ERP đã sẵn sàng sử dụng!**

Chúc bạn có trải nghiệm tốt với hệ thống!
