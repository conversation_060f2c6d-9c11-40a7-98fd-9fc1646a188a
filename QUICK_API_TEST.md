# 🚀 Quick API Test - 2 Minutes Setup

## ✅ API Integration đã hoàn thành

### 🔧 **Đã sửa tất cả endpoints:**
- ✅ Product API: `/api/v1/product`
- ✅ Material API: `/api/v1/material`
- ✅ Data mapping: Backend ↔ Frontend
- ✅ Error handling và response format

---

## 🧪 **Test ngay trong 2 phút:**

### **1. Khởi động hệ thống (30 giây)**
```bash
# Terminal 1: Start frontend
npm run dev
# Frontend: http://localhost:5173

# Terminal 2: <PERSON><PERSON>m bảo backend chạy
# Backend: http://localhost:8080
```

### **2. Test API Connection (30 giây)**
1. Vào: `http://localhost:5173/api-test`
2. Click **"Test Connection"** 
3. Xem status: ✅ Success hoặc ❌ Failed

### **3. Test Product API (30 giây)**
1. Click **"Test Get Products"**
2. Xem response trong console
3. Click **"Test Create Product"** (optional)

### **4. Test Material API (30 giây)**
1. Click **"Test Get Materials"**
2. Xem response trong console  
3. Click **"Test Create Material"** (optional)

---

## 📊 **Expected Results:**

### ✅ **Success Response:**
```json
{
  "success": true,
  "data": [
    {
      "material_id": 1,
      "material_name": "紅色染料",
      "supplier": "供應商A",
      "unit_cost": 20,
      "unit": "kg"
    }
  ],
  "message": "success"
}
```

### ❌ **Error Response:**
```json
{
  "success": false,
  "error": "Network Error"
}
```

---

## 🔧 **Nếu có lỗi:**

### **1. CORS Error:**
```
Access to fetch blocked by CORS policy
```
**Fix:** Enable CORS trong backend cho `http://localhost:5173`

### **2. 404 Error:**
```
{
  "code": 404,
  "message": "The processing function of the request route was not found"
}
```
**Fix:** Kiểm tra backend có endpoint `/api/v1/material` và `/api/v1/product`

### **3. Network Error:**
```
Network Error
```
**Fix:** Kiểm tra backend đang chạy trên port 8080

---

## 🎯 **Test trong Production App:**

### **1. Test Material List:**
```
http://localhost:5173/materials
```
- Mở F12 → Console
- Xem log: "Material response: ..."
- Data hiển thị trong table

### **2. Test Product List:**
```
http://localhost:5173/products  
```
- Mở F12 → Console
- Xem log: "Product response: ..."
- Data hiển thị trong table

---

## 📱 **Test Responsive Design:**

### **Desktop (≥1024px):**
- Full table layout
- Sidebar cố định
- Hover effects

### **Tablet (768px-1024px):**
- Sidebar thu gọn
- Adaptive columns

### **Mobile (<768px):**
- Card view thay vì table
- Hamburger menu
- Touch-friendly buttons

---

## 🎨 **Test UX Features:**

### **1. Quick Actions:**
- Click floating button (góc phải)
- Test wizard workflow
- Thử các thao tác nhanh

### **2. Loading States:**
- Skeleton loading khi tải data
- Loading animations
- Error boundaries

### **3. Charts:**
- Vào `/reports/inventory`
- Interactive charts
- Responsive trên mobile

---

## ✅ **Verification Checklist:**

- [ ] ✅ Backend running on port 8080
- [ ] ✅ Frontend running on port 5173
- [ ] ✅ API Test page loads
- [ ] ✅ Connection test passes
- [ ] ✅ Material API returns data
- [ ] ✅ Product API returns data
- [ ] ✅ Material list displays correctly
- [ ] ✅ Product list displays correctly
- [ ] ✅ Responsive design works
- [ ] ✅ Quick actions functional
- [ ] ✅ Charts render properly

---

## 🚀 **Ready for Production!**

Nếu tất cả tests pass, hệ thống đã sẵn sàng:
- ✅ API integration hoàn chỉnh
- ✅ Responsive design
- ✅ Modern UX/UI
- ✅ Error handling
- ✅ Performance optimized

**🎉 Chúc mừng! ERP System đã hoàn thành!**
