# API Integration Test Guide

## 🔧 Đã sửa các vấn đề API

### 1. **Endpoint URLs đã được cập nhật:**
- ✅ `/products` → `/product` 
- ✅ Base URL: `http://localhost:8080/api/v1`

### 2. **Response Format Handler:**
```javascript
// API sẽ xử lý format response từ backend:
{
  "code": 200,
  "data": [...],
  "message": "success"
}

// Và convert thành format frontend:
{
  "success": true,
  "data": [...],
  "message": "success"
}
```

### 3. **Data Mapping:**
- ✅ Backend → Frontend field mapping
- ✅ Frontend → Backend field mapping

## 🧪 Test API Connection

### 1. **Kiểm tra Backend Server:**
```bash
# Đảm bảo backend đang chạy trên port 8080
curl http://localhost:8080/api/v1/product
```

### 2. **Test trong Frontend:**

#### A. Mở Browser Console (F12)
```javascript
// Test API connection
fetch('http://localhost:8080/api/v1/product')
  .then(response => response.json())
  .then(data => console.log('API Response:', data))
  .catch(error => console.error('API Error:', error))
```

#### B. Test trong Vue Component
```javascript
// Trong ProductList.vue, check console log
// Sẽ thấy: "Product response: { success: true, data: [...] }"
```

### 3. **Expected Data Format:**

#### Backend Response:
```json
{
  "code": 200,
  "data": [
    {
      "id": 2,
      "productCode": "P00001",
      "productName": "塑膠墊",
      "productWeight": 350.5,
      "injectionTime": 12.3,
      "color": "紅色",
      "unitPrice": 79.99,
      "createdAt": "2025-06-02T09:53:08.737321Z",
      "updatedAt": "2025-06-02T09:53:08.825505Z"
    }
  ],
  "message": "success"
}
```

#### Frontend Format (after mapping):
```json
{
  "success": true,
  "data": [
    {
      "product_id": 2,
      "product_code": "P00001", 
      "product_name": "塑膠墊",
      "product_weight_g": 350.5,
      "injection_time_s": 12.3,
      "color": "紅色",
      "unit_price": 79.99,
      "created_at": "2025-06-02T09:53:08.737321Z",
      "updated_at": "2025-06-02T09:53:08.825505Z"
    }
  ],
  "message": "success"
}
```

## 🔧 Cấu hình CORS (nếu cần)

### Backend cần enable CORS cho frontend:
```javascript
// Express.js example
app.use(cors({
  origin: 'http://localhost:5173', // Vite dev server
  credentials: true
}))
```

## 🚀 Test Steps

### 1. **Start Backend Server**
```bash
# Đảm bảo backend chạy trên port 8080
```

### 2. **Start Frontend**
```bash
npm run dev
# Frontend chạy trên http://localhost:5173
```

### 3. **Test Product List**
1. Vào `/products` 
2. Mở F12 → Console
3. Xem log: "Product response: ..."
4. Kiểm tra data hiển thị trong table

### 4. **Test Create Product**
1. Click "Thêm sản phẩm"
2. Điền form với data:
   ```
   Tên sản phẩm: Ultra Cushion Shoe
   Trọng lượng: 350.5
   Thời gian ép: 12.3
   Màu sắc: Black
   Giá đơn vị: 79.99
   ```
3. Submit và check console

## 🐛 Troubleshooting

### 1. **CORS Error:**
```
Access to fetch at 'http://localhost:8080/api/v1/product' from origin 'http://localhost:5173' has been blocked by CORS policy
```
**Solution:** Enable CORS trong backend

### 2. **404 Error:**
```
{
  "code": 404,
  "message": "The processing function of the request route was not found"
}
```
**Solution:** Kiểm tra backend route handler cho `/api/v1/product`

### 3. **Network Error:**
```
Network Error
```
**Solution:** 
- Kiểm tra backend server đang chạy
- Kiểm tra URL đúng: `http://localhost:8080/api/v1/product`

### 4. **Data không hiển thị:**
- Check console log trong ProductList
- Verify data mapping trong productService.js

## 📝 API Endpoints Summary

| Method | Endpoint | Purpose | Body Format |
|--------|----------|---------|-------------|
| GET | `/api/v1/product` | Get all products | - |
| GET | `/api/v1/product/{id}` | Get product by ID | - |
| POST | `/api/v1/product` | Create product | `{ productName, productWeight, injectionTime, color, unitPrice }` |
| PUT | `/api/v1/product/{id}` | Update product | `{ productName, productWeight, injectionTime, color, unitPrice }` |
| DELETE | `/api/v1/product/{id}` | Delete product | - |

## ✅ Verification Checklist

- [ ] Backend server running on port 8080
- [ ] Frontend can access `/api/v1/product`
- [ ] CORS configured properly
- [ ] Data mapping working correctly
- [ ] Console shows proper response format
- [ ] Product list displays data
- [ ] Create/Update/Delete operations work

---

**🎯 Next Steps:**
1. Test API connection
2. Verify data display in ProductList
3. Test CRUD operations
4. Apply same fixes to MaterialService if needed
