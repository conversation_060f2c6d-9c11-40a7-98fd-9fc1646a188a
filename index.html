<!doctype html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/product-management.png" />
    <meta
      name="viewport"
      content="width=device-width, height=device-height, initial-scale=1.0"
    />
    <title>Tempalte</title>
  </head>

  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
<style>
  html,
  body {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    padding: 0px;
    margin: 0px;
  }
  html {
    font-size: 18px;
  }
  body {
    font-size: 1rem;
  }
  /* (600px - 900px) */
  @media only screen and (min-width: 600px) and (max-width: 900px) {
    html {
      font-size: 16px;
    }
  }
  /* (600px) */
  @media only screen and (max-width: 599px) {
    html {
      font-size: 14px;
    }
  }
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }
</style>
