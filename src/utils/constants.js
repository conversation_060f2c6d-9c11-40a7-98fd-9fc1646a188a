/**
 * Constants cho hệ thống ERP
 */

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  LOGIN: '/auth/login',
  LOGOUT: '/auth/logout',
  REFRESH: '/auth/refresh',
  
  // Materials
  MATERIALS: '/materials',
  MATERIAL_INVENTORY: '/inventories/material',
  MATERIAL_MOVEMENTS: '/movements/material',
  
  // Products
  PRODUCTS: '/products',
  PRODUCT_INVENTORY: '/inventories/product',
  PRODUCT_MOVEMENTS: '/movements/product',
  
  // BOM
  PRODUCT_BOM: '/product-bom',
  
  // Orders
  ORDERS: '/orders',
  ORDER_DETAILS: '/order-details',
  
  // Movements
  MOVEMENTS: '/movements',
  
  // Reports
  REPORTS: '/reports'
}

// Trạng thái đơn hàng
export const ORDER_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  IN_PRODUCTION: 'in_production',
  COMPLETED: 'completed',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled'
}

export const ORDER_STATUS_LABELS = {
  [ORDER_STATUS.PENDING]: 'Chờ xác nhận',
  [ORDER_STATUS.CONFIRMED]: 'Đã xác nhận',
  [ORDER_STATUS.IN_PRODUCTION]: 'Đang sản xuất',
  [ORDER_STATUS.COMPLETED]: 'Hoàn thành',
  [ORDER_STATUS.DELIVERED]: 'Đã giao hàng',
  [ORDER_STATUS.CANCELLED]: 'Đã hủy'
}

export const ORDER_STATUS_COLORS = {
  [ORDER_STATUS.PENDING]: 'warning',
  [ORDER_STATUS.CONFIRMED]: 'primary',
  [ORDER_STATUS.IN_PRODUCTION]: 'info',
  [ORDER_STATUS.COMPLETED]: 'success',
  [ORDER_STATUS.DELIVERED]: 'success',
  [ORDER_STATUS.CANCELLED]: 'danger'
}

// Loại giao dịch
export const MOVEMENT_TYPES = {
  IN: 'IN',
  OUT: 'OUT'
}

export const MOVEMENT_TYPE_LABELS = {
  [MOVEMENT_TYPES.IN]: 'Nhập kho',
  [MOVEMENT_TYPES.OUT]: 'Xuất kho'
}

export const MOVEMENT_TYPE_COLORS = {
  [MOVEMENT_TYPES.IN]: 'success',
  [MOVEMENT_TYPES.OUT]: 'warning'
}

// Đơn vị tính phổ biến
export const COMMON_UNITS = [
  // Khối lượng
  'kg', 'g', 'tấn', 'tạ', 'yến', 'lượng',
  
  // Thể tích
  'lít', 'ml', 'm³', 'cm³', 'gallon',
  
  // Chiều dài
  'mét', 'm', 'cm', 'mm', 'km', 'inch', 'feet',
  
  // Diện tích
  'm²', 'cm²', 'mm²', 'hecta',
  
  // Số lượng
  'cái', 'chiếc', 'bộ', 'gói', 'thùng', 'hộp', 'túi', 'cuộn', 'tấm', 'thanh'
]

// Màu sắc phổ biến
export const COMMON_COLORS = [
  { label: 'Đỏ', value: 'Đỏ', code: '#f56c6c' },
  { label: 'Xanh dương', value: 'Xanh', code: '#409eff' },
  { label: 'Vàng', value: 'Vàng', code: '#e6a23c' },
  { label: 'Xanh lá', value: 'Xanh lá', code: '#67c23a' },
  { label: 'Tím', value: 'Tím', code: '#9c27b0' },
  { label: 'Cam', value: 'Cam', code: '#ff9800' },
  { label: 'Hồng', value: 'Hồng', code: '#e91e63' },
  { label: 'Nâu', value: 'Nâu', code: '#795548' },
  { label: 'Xám', value: 'Xám', code: '#9e9e9e' },
  { label: 'Đen', value: 'Đen', code: '#424242' },
  { label: 'Trắng', value: 'Trắng', code: '#ffffff' }
]

// Cấu hình phân trang
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZES: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 1000
}

// Cấu hình tìm kiếm
export const SEARCH_CONFIG = {
  DEBOUNCE_DELAY: 300,
  MIN_SEARCH_LENGTH: 2,
  MAX_SEARCH_LENGTH: 100
}

// Cấu hình file upload
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_DOCUMENT_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ]
}

// Cấu hình validation
export const VALIDATION_RULES = {
  REQUIRED: { required: true, message: 'Trường này là bắt buộc', trigger: 'blur' },
  EMAIL: {
    type: 'email',
    message: 'Vui lòng nhập email hợp lệ',
    trigger: 'blur'
  },
  PHONE: {
    pattern: /^(0|\+84)[3|5|7|8|9][0-9]{8}$/,
    message: 'Vui lòng nhập số điện thoại hợp lệ',
    trigger: 'blur'
  },
  POSITIVE_NUMBER: {
    type: 'number',
    min: 0,
    message: 'Giá trị phải lớn hơn hoặc bằng 0',
    trigger: 'blur'
  },
  POSITIVE_INTEGER: {
    type: 'integer',
    min: 1,
    message: 'Giá trị phải là số nguyên dương',
    trigger: 'blur'
  }
}

// Cấu hình thông báo
export const NOTIFICATION_CONFIG = {
  DURATION: 3000,
  POSITION: 'top-right'
}

// Cấu hình theme
export const THEME_CONFIG = {
  PRIMARY_COLOR: '#e7ab3c',
  SUCCESS_COLOR: '#67c23a',
  WARNING_COLOR: '#e6a23c',
  DANGER_COLOR: '#f56c6c',
  INFO_COLOR: '#409eff',
  TEXT_COLOR: '#252525',
  BACKGROUND_COLOR: '#ffffff'
}

// Cấu hình local storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_INFO: 'user_info',
  SETTINGS: 'app_settings',
  THEME: 'app_theme',
  LANGUAGE: 'app_language'
}

// Cấu hình ngôn ngữ
export const LANGUAGE_CONFIG = {
  DEFAULT: 'vi',
  SUPPORTED: ['vi', 'en'],
  LABELS: {
    vi: 'Tiếng Việt',
    en: 'English'
  }
}

// Cấu hình báo cáo
export const REPORT_CONFIG = {
  EXPORT_FORMATS: ['pdf', 'excel', 'csv'],
  DATE_RANGES: [
    { label: 'Hôm nay', value: 'today' },
    { label: 'Tuần này', value: 'this_week' },
    { label: 'Tháng này', value: 'this_month' },
    { label: 'Quý này', value: 'this_quarter' },
    { label: 'Năm này', value: 'this_year' },
    { label: 'Tùy chọn', value: 'custom' }
  ]
}

// Cấu hình dashboard
export const DASHBOARD_CONFIG = {
  REFRESH_INTERVAL: 30000, // 30 seconds
  CHART_COLORS: [
    '#e7ab3c', '#67c23a', '#409eff', '#f56c6c', '#e6a23c',
    '#9c27b0', '#ff9800', '#e91e63', '#795548', '#9e9e9e'
  ]
}

// Cấu hình bảo mật
export const SECURITY_CONFIG = {
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  SESSION_TIMEOUT: 60 * 60 * 1000, // 1 hour
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000 // 15 minutes
}

// Export default
export default {
  API_ENDPOINTS,
  ORDER_STATUS,
  ORDER_STATUS_LABELS,
  ORDER_STATUS_COLORS,
  MOVEMENT_TYPES,
  MOVEMENT_TYPE_LABELS,
  MOVEMENT_TYPE_COLORS,
  COMMON_UNITS,
  COMMON_COLORS,
  PAGINATION_CONFIG,
  SEARCH_CONFIG,
  UPLOAD_CONFIG,
  VALIDATION_RULES,
  NOTIFICATION_CONFIG,
  THEME_CONFIG,
  STORAGE_KEYS,
  LANGUAGE_CONFIG,
  REPORT_CONFIG,
  DASHBOARD_CONFIG,
  SECURITY_CONFIG
}
