/**
 * Utility functions cho hệ thống ERP
 */

/**
 * Format currency theo định dạng Việt Nam
 * @param {number} amount - Số tiền
 * @param {string} currency - Loại tiền tệ (mặc định: VND)
 * @returns {string} Chuỗi tiền tệ đã format
 */
export const formatCurrency = (amount, currency = 'VND') => {
  if (amount === null || amount === undefined) return '0 ₫'
  
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

/**
 * Format số với dấu phân cách hàng nghìn
 * @param {number} number - Số cần format
 * @returns {string} Số đã format
 */
export const formatNumber = (number) => {
  if (number === null || number === undefined) return '0'
  
  return new Intl.NumberFormat('vi-VN').format(number)
}

/**
 * Format ngày tháng theo định dạng Việt Nam
 * @param {string|Date} date - Ngày cần format
 * @param {string} format - Đ<PERSON>nh dạng (short, long, datetime)
 * @returns {string} Ngày đã format
 */
export const formatDate = (date, format = 'short') => {
  if (!date) return '-'
  
  const dateObj = new Date(date)
  
  if (isNaN(dateObj.getTime())) return '-'
  
  const options = {
    short: { day: '2-digit', month: '2-digit', year: 'numeric' },
    long: { 
      weekday: 'long', 
      day: '2-digit', 
      month: 'long', 
      year: 'numeric' 
    },
    datetime: { 
      day: '2-digit', 
      month: '2-digit', 
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }
  }
  
  return dateObj.toLocaleDateString('vi-VN', options[format] || options.short)
}

/**
 * Debounce function để giảm số lần gọi hàm
 * @param {Function} func - Hàm cần debounce
 * @param {number} wait - Thời gian chờ (ms)
 * @returns {Function} Hàm đã debounce
 */
export const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * Throttle function để giới hạn số lần gọi hàm
 * @param {Function} func - Hàm cần throttle
 * @param {number} limit - Giới hạn thời gian (ms)
 * @returns {Function} Hàm đã throttle
 */
export const throttle = (func, limit) => {
  let inThrottle
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * Tạo ID ngẫu nhiên
 * @param {number} length - Độ dài ID
 * @returns {string} ID ngẫu nhiên
 */
export const generateId = (length = 8) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * Validate email
 * @param {string} email - Email cần validate
 * @returns {boolean} True nếu email hợp lệ
 */
export const validateEmail = (email) => {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return re.test(email)
}

/**
 * Validate số điện thoại Việt Nam
 * @param {string} phone - Số điện thoại cần validate
 * @returns {boolean} True nếu số điện thoại hợp lệ
 */
export const validatePhone = (phone) => {
  const re = /^(0|\+84)[3|5|7|8|9][0-9]{8}$/
  return re.test(phone.replace(/\s/g, ''))
}

/**
 * Chuyển đổi chuỗi thành slug
 * @param {string} str - Chuỗi cần chuyển đổi
 * @returns {string} Slug
 */
export const slugify = (str) => {
  return str
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '')
}

/**
 * Deep clone object
 * @param {any} obj - Object cần clone
 * @returns {any} Object đã clone
 */
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * Kiểm tra object rỗng
 * @param {object} obj - Object cần kiểm tra
 * @returns {boolean} True nếu object rỗng
 */
export const isEmpty = (obj) => {
  if (obj === null || obj === undefined) return true
  if (typeof obj === 'string') return obj.trim().length === 0
  if (Array.isArray(obj)) return obj.length === 0
  if (typeof obj === 'object') return Object.keys(obj).length === 0
  return false
}

/**
 * Tính toán phần trăm
 * @param {number} value - Giá trị
 * @param {number} total - Tổng
 * @param {number} decimals - Số chữ số thập phân
 * @returns {number} Phần trăm
 */
export const calculatePercentage = (value, total, decimals = 2) => {
  if (total === 0) return 0
  return Number(((value / total) * 100).toFixed(decimals))
}

/**
 * Tạo màu ngẫu nhiên
 * @returns {string} Mã màu hex
 */
export const generateRandomColor = () => {
  return '#' + Math.floor(Math.random() * 16777215).toString(16)
}

/**
 * Chuyển đổi bytes thành định dạng dễ đọc
 * @param {number} bytes - Số bytes
 * @param {number} decimals - Số chữ số thập phân
 * @returns {string} Kích thước đã format
 */
export const formatBytes = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * Lấy thông tin trình duyệt
 * @returns {object} Thông tin trình duyệt
 */
export const getBrowserInfo = () => {
  const ua = navigator.userAgent
  let browser = 'Unknown'
  let version = 'Unknown'
  
  if (ua.indexOf('Chrome') > -1) {
    browser = 'Chrome'
    version = ua.match(/Chrome\/(\d+)/)?.[1] || 'Unknown'
  } else if (ua.indexOf('Firefox') > -1) {
    browser = 'Firefox'
    version = ua.match(/Firefox\/(\d+)/)?.[1] || 'Unknown'
  } else if (ua.indexOf('Safari') > -1) {
    browser = 'Safari'
    version = ua.match(/Version\/(\d+)/)?.[1] || 'Unknown'
  } else if (ua.indexOf('Edge') > -1) {
    browser = 'Edge'
    version = ua.match(/Edge\/(\d+)/)?.[1] || 'Unknown'
  }
  
  return { browser, version }
}

/**
 * Export tất cả utilities
 */
export default {
  formatCurrency,
  formatNumber,
  formatDate,
  debounce,
  throttle,
  generateId,
  validateEmail,
  validatePhone,
  slugify,
  deepClone,
  isEmpty,
  calculatePercentage,
  generateRandomColor,
  formatBytes,
  getBrowserInfo
}
