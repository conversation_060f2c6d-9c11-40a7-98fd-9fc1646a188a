import apiClient from './api'

/**
 * Helper functions để map data giữa frontend và backend
 */
const mapToBackendFormat = (frontendData) => {
  return {
    MaterialName: frontendData.material_name || frontendData.MaterialName,
    Supplier: frontendData.supplier || frontendData.Supplier,
    UnitCost: frontendData.unit_cost || frontendData.UnitCost,
    Unit: frontendData.unit || frontendData.Unit
  }
}

const mapToFrontendFormat = (backendData) => {
  return {
    material_id: backendData.ID,
    material_code: backendData.MaterialCode,
    material_name: backendData.MaterialName,
    supplier: backendData.Supplier,
    unit_cost: backendData.UnitCost,
    unit: backendData.Unit,
    created_at: backendData.CreatedAt,
    deleted: backendData.Deleted
  }
}

class MaterialService {
  /**
   * Lấy danh sách nguyên vật liệu
   * @param {Object} params - T<PERSON> số query
   * @param {number} params.page - Trang hiện tại
   * @param {number} params.limit - <PERSON><PERSON> lượng item trên mỗi trang
   * @param {string} params.search - Từ khóa tìm kiếm
   * @returns {Promise} Response từ API
   */
  async getMaterials(params = {}) {
    try {
      const response = await apiClient.get('/material', { params })

      // Map backend data to frontend format
      if (response.success && Array.isArray(response.data)) {
        response.data = response.data.map(mapToFrontendFormat)
      }

      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Lấy thông tin chi tiết nguyên vật liệu
   * @param {number} materialId - ID nguyên vật liệu
   * @returns {Promise} Response từ API
   */
  async getMaterialById(materialId) {
    try {
      const response = await apiClient.get(`/material/${materialId}`)

      // Map backend data to frontend format
      if (response.success && response.data) {
        response.data = mapToFrontendFormat(response.data)
      }

      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Tạo mới nguyên vật liệu
   * @param {Object} materialData - Dữ liệu nguyên vật liệu
   * @param {string} materialData.material_name - Tên nguyên vật liệu
   * @param {string} materialData.supplier - Nhà cung cấp
   * @param {number} materialData.unit_cost - Giá đơn vị
   * @param {string} materialData.unit - Đơn vị tính
   * @returns {Promise} Response từ API
   */
  async createMaterial(materialData) {
    try {
      // Map frontend fields to backend fields using helper function
      const backendData = mapToBackendFormat(materialData)

      const response = await apiClient.post('/material', backendData)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Cập nhật nguyên vật liệu
   * @param {number} materialId - ID nguyên vật liệu
   * @param {Object} materialData - Dữ liệu cập nhật
   * @returns {Promise} Response từ API
   */
  async updateMaterial(materialId, materialData) {
    try {
      // Map frontend fields to backend fields using helper function
      const backendData = mapToBackendFormat(materialData)

      const response = await apiClient.put(`/material/${materialId}`, backendData)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Xóa nguyên vật liệu
   * @param {number} materialId - ID nguyên vật liệu
   * @returns {Promise} Response từ API
   */
  async deleteMaterial(materialId) {
    try {
      const response = await apiClient.delete(`/material/${materialId}`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Lấy danh sách tồn kho nguyên vật liệu
   * @param {Object} params - Tham số query
   * @returns {Promise} Response từ API
   */
  async getMaterialInventory(params = {}) {
    try {
      const response = await apiClient.get('/inventories/material', { params })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Lấy lịch sử nhập xuất nguyên vật liệu
   * @param {Object} params - Tham số query
   * @returns {Promise} Response từ API
   */
  async getMaterialMovements(params = {}) {
    try {
      const response = await apiClient.get('/movements/material', { params })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Ghi nhận nhập kho nguyên vật liệu
   * @param {Object} movementData - Dữ liệu nhập kho
   * @param {number} movementData.material_id - ID nguyên vật liệu
   * @param {number} movementData.quantity - Số lượng
   * @param {string} movementData.remark - Ghi chú
   * @returns {Promise} Response từ API
   */
  async recordMaterialInbound(movementData) {
    try {
      const data = {
        ...movementData,
        movement_type: 'IN'
      }
      const response = await apiClient.post('/movements', data)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Ghi nhận xuất kho nguyên vật liệu
   * @param {Object} movementData - Dữ liệu xuất kho
   * @returns {Promise} Response từ API
   */
  async recordMaterialOutbound(movementData) {
    try {
      const data = {
        ...movementData,
        movement_type: 'OUT'
      }
      const response = await apiClient.post('/movements', data)
      return response
    } catch (error) {
      throw error
    }
  }
}

export default new MaterialService()
