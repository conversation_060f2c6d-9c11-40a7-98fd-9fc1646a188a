import apiClient from './api'

class MaterialService {
  /**
   * <PERSON><PERSON><PERSON> danh sách nguyên vật liệu
   * @param {Object} params - Tham số query
   * @param {number} params.page - Trang hiện tại
   * @param {number} params.limit - Số lượng item trên mỗi trang
   * @param {string} params.search - Từ khóa tìm kiếm
   * @returns {Promise} Response từ API
   */
  async getMaterials(params = {}) {
    try {
      const response = await apiClient.get('/materials', { params })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * L<PERSON>y thông tin chi tiết nguyên vật liệu
   * @param {number} materialId - ID nguyên vật liệu
   * @returns {Promise} Response từ API
   */
  async getMaterialById(materialId) {
    try {
      const response = await apiClient.get(`/materials/${materialId}`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * T<PERSON><PERSON> mới nguyên vật liệu
   * @param {Object} materialData - <PERSON><PERSON> liệu nguyên vật liệu
   * @param {string} materialData.material_name - Tên nguyên vật liệu
   * @param {string} materialData.supplier - Nhà cung cấp
   * @param {number} materialData.unit_cost - Giá đơn vị
   * @param {string} materialData.unit - Đơn vị tính
   * @returns {Promise} Response từ API
   */
  async createMaterial(materialData) {
    try {
      const response = await apiClient.post('/materials', materialData)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Cập nhật nguyên vật liệu
   * @param {number} materialId - ID nguyên vật liệu
   * @param {Object} materialData - Dữ liệu cập nhật
   * @returns {Promise} Response từ API
   */
  async updateMaterial(materialId, materialData) {
    try {
      const response = await apiClient.put(`/materials/${materialId}`, materialData)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Xóa nguyên vật liệu
   * @param {number} materialId - ID nguyên vật liệu
   * @returns {Promise} Response từ API
   */
  async deleteMaterial(materialId) {
    try {
      const response = await apiClient.delete(`/materials/${materialId}`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Lấy danh sách tồn kho nguyên vật liệu
   * @param {Object} params - Tham số query
   * @returns {Promise} Response từ API
   */
  async getMaterialInventory(params = {}) {
    try {
      const response = await apiClient.get('/inventories/material', { params })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Lấy lịch sử nhập xuất nguyên vật liệu
   * @param {Object} params - Tham số query
   * @returns {Promise} Response từ API
   */
  async getMaterialMovements(params = {}) {
    try {
      const response = await apiClient.get('/movements/material', { params })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Ghi nhận nhập kho nguyên vật liệu
   * @param {Object} movementData - Dữ liệu nhập kho
   * @param {number} movementData.material_id - ID nguyên vật liệu
   * @param {number} movementData.quantity - Số lượng
   * @param {string} movementData.remark - Ghi chú
   * @returns {Promise} Response từ API
   */
  async recordMaterialInbound(movementData) {
    try {
      const data = {
        ...movementData,
        movement_type: 'IN'
      }
      const response = await apiClient.post('/movements', data)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Ghi nhận xuất kho nguyên vật liệu
   * @param {Object} movementData - Dữ liệu xuất kho
   * @returns {Promise} Response từ API
   */
  async recordMaterialOutbound(movementData) {
    try {
      const data = {
        ...movementData,
        movement_type: 'OUT'
      }
      const response = await apiClient.post('/movements', data)
      return response
    } catch (error) {
      throw error
    }
  }
}

export default new MaterialService()
