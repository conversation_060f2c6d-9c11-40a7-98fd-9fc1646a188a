import apiClient from './api'

class ProductService {
  /**
   * <PERSON><PERSON><PERSON>nh s<PERSON>ch sản phẩm
   * @param {Object} params - Tham số query
   * @returns {Promise} Response từ API
   */
  async getProducts(params = {}) {
    try {
      const response = await apiClient.get('/products', { params })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * <PERSON><PERSON><PERSON> thông tin chi tiết sản phẩm
   * @param {number} productId - ID sản phẩm
   * @returns {Promise} Response từ API
   */
  async getProductById(productId) {
    try {
      const response = await apiClient.get(`/products/${productId}`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Tạo mới sản phẩm
   * @param {Object} productData - Dữ liệu sản phẩm
   * @param {string} productData.product_name - Tên sản phẩm
   * @param {number} productData.product_weight_g - Trọ<PERSON> l<PERSON> (gram)
   * @param {number} productData.injection_time_s - Thờ<PERSON> gian <PERSON> (giây)
   * @param {string} productData.color - M<PERSON><PERSON> sắc
   * @param {number} productData.unit_price - Giá đơn vị
   * @returns {Promise} Response từ API
   */
  async createProduct(productData) {
    try {
      const response = await apiClient.post('/products', productData)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Cập nhật sản phẩm
   * @param {number} productId - ID sản phẩm
   * @param {Object} productData - Dữ liệu cập nhật
   * @returns {Promise} Response từ API
   */
  async updateProduct(productId, productData) {
    try {
      const response = await apiClient.put(`/products/${productId}`, productData)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Xóa sản phẩm
   * @param {number} productId - ID sản phẩm
   * @returns {Promise} Response từ API
   */
  async deleteProduct(productId) {
    try {
      const response = await apiClient.delete(`/products/${productId}`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Lấy danh sách tồn kho sản phẩm
   * @param {Object} params - Tham số query
   * @returns {Promise} Response từ API
   */
  async getProductInventory(params = {}) {
    try {
      const response = await apiClient.get('/inventories/product', { params })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Lấy lịch sử nhập xuất sản phẩm
   * @param {Object} params - Tham số query
   * @returns {Promise} Response từ API
   */
  async getProductMovements(params = {}) {
    try {
      const response = await apiClient.get('/movements/product', { params })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Ghi nhận nhập kho sản phẩm (sản xuất)
   * @param {Object} movementData - Dữ liệu nhập kho
   * @param {number} movementData.product_id - ID sản phẩm
   * @param {number} movementData.quantity - Số lượng
   * @param {string} movementData.remark - Ghi chú
   * @returns {Promise} Response từ API
   */
  async recordProductInbound(movementData) {
    try {
      const data = {
        ...movementData,
        movement_type: 'IN'
      }
      const response = await apiClient.post('/movements', data)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Ghi nhận xuất kho sản phẩm (giao hàng)
   * @param {Object} movementData - Dữ liệu xuất kho
   * @returns {Promise} Response từ API
   */
  async recordProductOutbound(movementData) {
    try {
      const data = {
        ...movementData,
        movement_type: 'OUT'
      }
      const response = await apiClient.post('/movements', data)
      return response
    } catch (error) {
      throw error
    }
  }
}

export default new ProductService()
