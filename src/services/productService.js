import apiClient from './api'

/**
 * Helper functions để map data giữa frontend và backend
 */
const mapToBackendFormat = (frontendData) => {
  return {
    productName: frontendData.product_name || frontendData.productName,
    productWeight: frontendData.product_weight_g || frontendData.productWeight,
    injectionTime: frontendData.injection_time_s || frontendData.injectionTime,
    color: frontendData.color,
    unitPrice: frontendData.unit_price || frontendData.unitPrice
  }
}

const mapToFrontendFormat = (backendData) => {
  return {
    product_id: backendData.id,
    product_name: backendData.productName,
    product_weight_g: backendData.productWeight,
    injection_time_s: backendData.injectionTime,
    color: backendData.color,
    unit_price: backendData.unitPrice,
    created_at: backendData.createdAt,
    updated_at: backendData.updatedAt,
    product_code: backendData.productCode
  }
}

class ProductService {
  /**
   * <PERSON><PERSON><PERSON> danh s<PERSON>ch sản phẩm
   * @param {Object} params - Tham số query
   * @returns {Promise} Response từ API
   */
  async getProducts(params = {}) {
    try {
      const response = await apiClient.get('/product', { params })

      // Map backend data to frontend format
      if (response.success && Array.isArray(response.data)) {
        response.data = response.data.map(mapToFrontendFormat)
      }

      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Lấy thông tin chi tiết sản phẩm
   * @param {number} productId - ID sản phẩm
   * @returns {Promise} Response từ API
   */
  async getProductById(productId) {
    try {
      const response = await apiClient.get(`/product/${productId}`)

      // Map backend data to frontend format
      if (response.success && response.data) {
        response.data = mapToFrontendFormat(response.data)
      }

      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Tạo mới sản phẩm
   * @param {Object} productData - Dữ liệu sản phẩm
   * @param {string} productData.product_name - Tên sản phẩm
   * @param {number} productData.product_weight_g - Trọng lượng (gram)
   * @param {number} productData.injection_time_s - Thời gian ép (giây)
   * @param {string} productData.color - Màu sắc
   * @param {number} productData.unit_price - Giá đơn vị
   * @returns {Promise} Response từ API
   */
  async createProduct(productData) {
    try {
      // Map frontend fields to backend fields using helper function
      const backendData = mapToBackendFormat(productData)

      const response = await apiClient.post('/product', backendData)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Cập nhật sản phẩm
   * @param {number} productId - ID sản phẩm
   * @param {Object} productData - Dữ liệu cập nhật
   * @returns {Promise} Response từ API
   */
  async updateProduct(productId, productData) {
    try {
      // Map frontend fields to backend fields using helper function
      const backendData = mapToBackendFormat(productData)

      const response = await apiClient.put(`/product/${productId}`, backendData)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Xóa sản phẩm
   * @param {number} productId - ID sản phẩm
   * @returns {Promise} Response từ API
   */
  async deleteProduct(productId) {
    try {
      const response = await apiClient.delete(`/product/${productId}`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Lấy danh sách tồn kho sản phẩm
   * @param {Object} params - Tham số query
   * @returns {Promise} Response từ API
   */
  async getProductInventory(params = {}) {
    try {
      const response = await apiClient.get('/inventories/product', { params })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Lấy lịch sử nhập xuất sản phẩm
   * @param {Object} params - Tham số query
   * @returns {Promise} Response từ API
   */
  async getProductMovements(params = {}) {
    try {
      const response = await apiClient.get('/movements/product', { params })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Ghi nhận nhập kho sản phẩm (sản xuất)
   * @param {Object} movementData - Dữ liệu nhập kho
   * @param {number} movementData.product_id - ID sản phẩm
   * @param {number} movementData.quantity - Số lượng
   * @param {string} movementData.remark - Ghi chú
   * @returns {Promise} Response từ API
   */
  async recordProductInbound(movementData) {
    try {
      const data = {
        ...movementData,
        movement_type: 'IN'
      }
      const response = await apiClient.post('/movements', data)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Ghi nhận xuất kho sản phẩm (giao hàng)
   * @param {Object} movementData - Dữ liệu xuất kho
   * @returns {Promise} Response từ API
   */
  async recordProductOutbound(movementData) {
    try {
      const data = {
        ...movementData,
        movement_type: 'OUT'
      }
      const response = await apiClient.post('/movements', data)
      return response
    } catch (error) {
      throw error
    }
  }
}

export default new ProductService()
