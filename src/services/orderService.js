import apiClient from './api'

class OrderService {
  /**
   * <PERSON><PERSON><PERSON> danh sách đơn hàng
   * @param {Object} params - Tham số query
   * @param {number} params.page - Trang hiện tại
   * @param {number} params.limit - <PERSON><PERSON> lượng item trên mỗi trang
   * @param {string} params.search - Từ khóa tìm kiếm
   * @param {string} params.status - Trạng thái đơn hàng
   * @returns {Promise} Response từ API
   */
  async getOrders(params = {}) {
    try {
      const response = await apiClient.get('/orders', { params })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Lấy thông tin chi tiết đơn hàng
   * @param {number} orderId - ID đơn hàng
   * @returns {Promise} Response từ API
   */
  async getOrderById(orderId) {
    try {
      const response = await apiClient.get(`/orders/${orderId}`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * T<PERSON><PERSON> mới đơn hàng
   * @param {Object} orderData - <PERSON><PERSON> liệu đơn hàng
   * @param {string} orderData.order_date - Ngày đặt hàng
   * @param {string} orderData.plan_delivery_date - Ngày giao hàng dự kiến
   * @param {string} orderData.actual_delivery_date - Ngày giao hàng thực tế (optional)
   * @returns {Promise} Response từ API
   */
  async createOrder(orderData) {
    try {
      const response = await apiClient.post('/orders', orderData)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Cập nhật đơn hàng
   * @param {number} orderId - ID đơn hàng
   * @param {Object} orderData - Dữ liệu cập nhật
   * @returns {Promise} Response từ API
   */
  async updateOrder(orderId, orderData) {
    try {
      const response = await apiClient.put(`/orders/${orderId}`, orderData)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Xóa đơn hàng
   * @param {number} orderId - ID đơn hàng
   * @returns {Promise} Response từ API
   */
  async deleteOrder(orderId) {
    try {
      const response = await apiClient.delete(`/orders/${orderId}`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Lấy chi tiết đơn hàng
   * @param {number} orderId - ID đơn hàng
   * @returns {Promise} Response từ API
   */
  async getOrderDetails(orderId) {
    try {
      const response = await apiClient.get(`/order-details/order/${orderId}`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Thêm sản phẩm vào đơn hàng
   * @param {Object} orderDetailData - Dữ liệu chi tiết đơn hàng
   * @param {number} orderDetailData.order_id - ID đơn hàng
   * @param {number} orderDetailData.product_id - ID sản phẩm
   * @param {number} orderDetailData.p_quantity - Số lượng sản phẩm
   * @returns {Promise} Response từ API
   */
  async addOrderDetail(orderDetailData) {
    try {
      const response = await apiClient.post('/order-details', orderDetailData)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Thêm nhiều sản phẩm vào đơn hàng
   * @param {number} orderId - ID đơn hàng
   * @param {Array} products - Danh sách sản phẩm
   * @param {number} products[].product_id - ID sản phẩm
   * @param {number} products[].p_quantity - Số lượng
   * @returns {Promise} Response từ API
   */
  async addOrderDetailsBatch(orderId, products) {
    try {
      const orderDetails = products.map(product => ({
        order_id: orderId,
        product_id: product.product_id,
        p_quantity: product.p_quantity
      }))
      
      const response = await apiClient.post('/order-details/batch', { order_details: orderDetails })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Cập nhật chi tiết đơn hàng
   * @param {number} orderDetailId - ID chi tiết đơn hàng
   * @param {Object} orderDetailData - Dữ liệu cập nhật
   * @returns {Promise} Response từ API
   */
  async updateOrderDetail(orderDetailId, orderDetailData) {
    try {
      const response = await apiClient.put(`/order-details/${orderDetailId}`, orderDetailData)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Xóa chi tiết đơn hàng
   * @param {number} orderDetailId - ID chi tiết đơn hàng
   * @returns {Promise} Response từ API
   */
  async deleteOrderDetail(orderDetailId) {
    try {
      const response = await apiClient.delete(`/order-details/${orderDetailId}`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Tính toán nguyên vật liệu cần cho đơn hàng
   * @param {number} orderId - ID đơn hàng
   * @returns {Promise} Response từ API
   */
  async calculateRequiredMaterials(orderId) {
    try {
      const response = await apiClient.get(`/orders/${orderId}/required-materials`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Cập nhật trạng thái giao hàng
   * @param {number} orderId - ID đơn hàng
   * @param {string} actualDeliveryDate - Ngày giao hàng thực tế
   * @returns {Promise} Response từ API
   */
  async updateDeliveryStatus(orderId, actualDeliveryDate) {
    try {
      const response = await apiClient.patch(`/orders/${orderId}/delivery`, {
        actual_delivery_date: actualDeliveryDate
      })
      return response
    } catch (error) {
      throw error
    }
  }
}

export default new OrderService()
