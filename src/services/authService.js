import apiClient from './api'

class AuthService {
  /**
   * Đăng nhập
   * @param {Object} credentials - Thông tin đăng nhập
   * @param {string} credentials.username - Tên đăng nhập
   * @param {string} credentials.password - M<PERSON><PERSON> khẩu
   * @returns {Promise} Response từ API
   */
  async login(credentials) {
    try {
      const response = await apiClient.post('/auth/login', credentials)
      
      if (response.success && response.data.token) {
        // Lưu token vào localStorage
        localStorage.setItem('auth_token', response.data.token)
        localStorage.setItem('user_info', JSON.stringify(response.data.user))
      }
      
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Đăng xuất
   */
  logout() {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_info')
    window.location.href = '/login'
  }

  /**
   * Kiểm tra trạng thái đăng nhập
   * @returns {boolean} True nếu đã đăng nhập
   */
  isAuthenticated() {
    const token = localStorage.getItem('auth_token')
    return !!token
  }

  /**
   * Lấy thông tin user hiện tại
   * @returns {Object|null} Thông tin user hoặc null
   */
  getCurrentUser() {
    const userInfo = localStorage.getItem('user_info')
    return userInfo ? JSON.parse(userInfo) : null
  }

  /**
   * Lấy token hiện tại
   * @returns {string|null} Token hoặc null
   */
  getToken() {
    return localStorage.getItem('auth_token')
  }

  /**
   * Refresh token
   * @returns {Promise} Response từ API
   */
  async refreshToken() {
    try {
      const response = await apiClient.post('/auth/refresh')
      
      if (response.success && response.data.token) {
        localStorage.setItem('auth_token', response.data.token)
      }
      
      return response
    } catch (error) {
      this.logout()
      throw error
    }
  }
}

export default new AuthService()
