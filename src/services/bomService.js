import apiClient from './api'

class BOMService {
  /**
   * <PERSON><PERSON><PERSON>nh sách BOM (Bill of Materials)
   * @param {Object} params - Tham số query
   * @param {number} params.page - Trang hiện tại
   * @param {number} params.limit - Số lượng item trên mỗi trang
   * @param {number} params.product_id - Lọc theo ID sản phẩm
   * @returns {Promise} Response từ API
   */
  async getBOMs(params = {}) {
    try {
      const response = await apiClient.get('/product-bom', { params })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Lấy BOM theo ID sản phẩm
   * @param {number} productId - ID sản phẩm
   * @returns {Promise} Response từ API
   */
  async getBOMByProductId(productId) {
    try {
      const response = await apiClient.get(`/product-bom/product/${productId}`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * L<PERSON>y thông tin chi tiết BOM
   * @param {number} bomId - ID BOM
   * @returns {Promise} Response từ API
   */
  async getBOMById(bomId) {
    try {
      const response = await apiClient.get(`/product-bom/${bomId}`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Tạo mới BOM
   * @param {Object} bomData - Dữ liệu BOM
   * @param {number} bomData.product_id - ID sản phẩm
   * @param {number} bomData.material_id - ID nguyên vật liệu
   * @param {number} bomData.m_quantity - Số lượng nguyên vật liệu cần
   * @returns {Promise} Response từ API
   */
  async createBOM(bomData) {
    try {
      const response = await apiClient.post('/product-bom', bomData)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Tạo nhiều BOM cùng lúc cho một sản phẩm
   * @param {number} productId - ID sản phẩm
   * @param {Array} materials - Danh sách nguyên vật liệu
   * @param {number} materials[].material_id - ID nguyên vật liệu
   * @param {number} materials[].m_quantity - Số lượng cần
   * @returns {Promise} Response từ API
   */
  async createBOMBatch(productId, materials) {
    try {
      const bomData = materials.map(material => ({
        product_id: productId,
        material_id: material.material_id,
        m_quantity: material.m_quantity
      }))
      
      const response = await apiClient.post('/product-bom/batch', { boms: bomData })
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Cập nhật BOM
   * @param {number} bomId - ID BOM
   * @param {Object} bomData - Dữ liệu cập nhật
   * @returns {Promise} Response từ API
   */
  async updateBOM(bomId, bomData) {
    try {
      const response = await apiClient.put(`/product-bom/${bomId}`, bomData)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Xóa BOM
   * @param {number} bomId - ID BOM
   * @returns {Promise} Response từ API
   */
  async deleteBOM(bomId) {
    try {
      const response = await apiClient.delete(`/product-bom/${bomId}`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Xóa tất cả BOM của một sản phẩm
   * @param {number} productId - ID sản phẩm
   * @returns {Promise} Response từ API
   */
  async deleteBOMByProductId(productId) {
    try {
      const response = await apiClient.delete(`/product-bom/product/${productId}`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Tính toán nguyên vật liệu cần cho đơn hàng
   * @param {number} orderId - ID đơn hàng
   * @returns {Promise} Response từ API
   */
  async calculateMaterialsForOrder(orderId) {
    try {
      const response = await apiClient.get(`/product-bom/calculate-materials/${orderId}`)
      return response
    } catch (error) {
      throw error
    }
  }

  /**
   * Kiểm tra tồn kho có đủ cho sản xuất không
   * @param {number} productId - ID sản phẩm
   * @param {number} quantity - Số lượng cần sản xuất
   * @returns {Promise} Response từ API
   */
  async checkMaterialAvailability(productId, quantity) {
    try {
      const response = await apiClient.post('/product-bom/check-availability', {
        product_id: productId,
        quantity: quantity
      })
      return response
    } catch (error) {
      throw error
    }
  }
}

export default new BOMService()
