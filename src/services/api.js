import axios from 'axios'
import { ElNotification } from 'element-plus'

// C<PERSON>u hình base URL cho API
const API_BASE_URL = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080/api/v1'

// Tạo instance axios với cấu hình mặc định
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// Request interceptor - Thêm token vào header
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor - Xử lý lỗi chung
apiClient.interceptors.response.use(
  (response) => {
    // Xử lý response format từ backend
    const data = response.data

    // Nếu backend trả về format { code, data, message }
    if (data && typeof data.code !== 'undefined') {
      if (data.code === 200) {
        return {
          success: true,
          data: data.data,
          message: data.message
        }
      } else {
        // Xử lý lỗi từ backend
        throw new Error(data.message || 'API Error')
      }
    }

    // Fallback cho format khác
    return {
      success: true,
      data: data,
      message: 'Success'
    }
  },
  (error) => {
    const { response } = error
    
    if (response) {
      switch (response.status) {
        case 401:
          // Token hết hạn hoặc không hợp lệ
          localStorage.removeItem('auth_token')
          window.location.href = '/login'
          ElNotification({
            title: 'Lỗi xác thực',
            message: 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.',
            type: 'error'
          })
          break
        case 403:
          ElNotification({
            title: 'Không có quyền',
            message: 'Bạn không có quyền thực hiện thao tác này.',
            type: 'error'
          })
          break
        case 404:
          ElNotification({
            title: 'Không tìm thấy',
            message: response.data?.message || 'Endpoint không tồn tại hoặc dữ liệu không tìm thấy.',
            type: 'error'
          })
          break
        case 500:
          ElNotification({
            title: 'Lỗi server',
            message: 'Có lỗi xảy ra từ phía server. Vui lòng thử lại sau.',
            type: 'error'
          })
          break
        default:
          ElNotification({
            title: 'Lỗi',
            message: response.data?.message || 'Có lỗi xảy ra. Vui lòng thử lại.',
            type: 'error'
          })
      }
    } else {
      ElNotification({
        title: 'Lỗi kết nối',
        message: 'Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.',
        type: 'error'
      })
    }
    
    return Promise.reject(error)
  }
)

export default apiClient
