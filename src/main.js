import { registerPlugins } from "@/plugins";
import App from "./App.vue";
import { createApp } from "vue";
import { createI18n } from "vue-i18n";
import router from "./router";
import zh from "./locales/zh.js";
import vi from "./locales/vi.js";
import { createVuetify } from "vuetify";
// import "@mdi/font/css/materialdesignicons.css";
import "element-plus/theme-chalk/dark/css-vars.css";
import * as components from "vuetify/components";
import * as directives from "vuetify/directives";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";

const i18n = createI18n({
  legacy: false,
  locale: "vn",
  fallbackLocale: "vn",
  missingWarn: true,
  fallbackWarn: true,
  messages: {
    zh,
    vi,
  },
});

const vuetify = createVuetify({
  components,
  directives,
  icons: {
    defaultSet: "mdi",
  },
});

const app = createApp(App);

registerPlugins(app);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
app.use(i18n);
app.use(router);
app.use(vuetify);
app.use(ElementPlus);
app.mount("#app");
