<template>
  <div class="bom-list">
    <!-- Header -->
    <div class="page-header">
      <div class="header-left">
        <h2><PERSON><PERSON><PERSON><PERSON> l<PERSON> mức <PERSON>uyên li<PERSON> (BOM)</h2>
        <p><PERSON>u<PERSON>n lý định mức nguyên vật liệu cần thiết cho từng sản phẩm</p>
      </div>
      <div class="header-right">
        <el-button 
          type="primary" 
          @click="showCreateDialog = true"
          :icon="Plus"
        >
          Thêm BOM
        </el-button>
      </div>
    </div>

    <!-- B<PERSON> lọc -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <el-select
            v-model="productFilter"
            placeholder="Lọc theo sản phẩm"
            clearable
            filterable
            @change="handleSearch"
            style="width: 100%"
          >
            <el-option
              v-for="product in products"
              :key="product.product_id"
              :label="product.product_name"
              :value="product.product_id"
            />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <el-select
            v-model="materialFilter"
            placeholder="Lọc theo nguyên vật liệu"
            clearable
            filterable
            @change="handleSearch"
            style="width: 100%"
          >
            <el-option
              v-for="material in materials"
              :key="material.material_id"
              :label="material.material_name"
              :value="material.material_id"
            />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <el-button @click="resetFilters" :icon="Refresh">
            Đặt lại bộ lọc
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- Bảng BOM -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="boms"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column
          prop="bom_id"
          label="BOM ID"
          width="100"
          sortable="custom"
        />
        <el-table-column
          prop="product_name"
          label="Sản phẩm"
          min-width="200"
          sortable="custom"
        >
          <template #default="{ row }">
            <div class="product-info">
              <strong>{{ row.product_name }}</strong>
              <div class="product-details">
                <el-tag size="small" :color="getColorCode(row.product_color)">
                  {{ row.product_color }}
                </el-tag>
                <span class="weight">{{ row.product_weight_g }}g</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="material_name"
          label="Nguyên vật liệu"
          min-width="200"
          sortable="custom"
        >
          <template #default="{ row }">
            <div class="material-info">
              <strong>{{ row.material_name }}</strong>
              <div class="supplier">{{ row.supplier }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="m_quantity"
          label="Định mức"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">
            <span class="quantity">{{ row.m_quantity }} {{ row.material_unit }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="Chi phí nguyên liệu"
          width="150"
        >
          <template #default="{ row }">
            {{ formatCurrency(row.m_quantity * row.unit_cost) }}
          </template>
        </el-table-column>
        <el-table-column
          label="Thao tác"
          width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="editBOM(row)"
              :icon="Edit"
            >
              Sửa
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteBOM(row)"
              :icon="Delete"
            >
              Xóa
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- Phân trang -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- Thống kê -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <h3>Thống kê BOM</h3>
          </template>
          <div class="stats-content">
            <div class="stat-item">
              <span class="stat-label">Tổng số BOM:</span>
              <span class="stat-value">{{ totalBOMs }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Số sản phẩm có BOM:</span>
              <span class="stat-value">{{ uniqueProducts }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Số nguyên liệu được sử dụng:</span>
              <span class="stat-value">{{ uniqueMaterials }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <h3>Thao tác nhanh</h3>
          </template>
          <div class="quick-actions">
            <el-button 
              type="success" 
              @click="calculateMaterials"
              :icon="Calculator"
              style="width: 100%; margin-bottom: 12px;"
            >
              Tính toán nguyên liệu cho đơn hàng
            </el-button>
            <el-button 
              type="info" 
              @click="checkAvailability"
              :icon="Search"
              style="width: 100%;"
            >
              Kiểm tra tồn kho nguyên liệu
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Dialog thêm/sửa BOM -->
    <BOMFormDialog
      v-model:visible="showCreateDialog"
      :bom="selectedBOM"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Refresh, 
  Edit, 
  Delete,
  Calculator,
  Search
} from '@element-plus/icons-vue'
import { bomService, productService, materialService } from '@/services'
import BOMFormDialog from '@/components/bom/BOMFormDialog.vue'

// Reactive data
const loading = ref(false)
const boms = ref([])
const products = ref([])
const materials = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const productFilter = ref('')
const materialFilter = ref('')
const showCreateDialog = ref(false)
const selectedBOM = ref(null)

// Sorting
const sortField = ref('')
const sortOrder = ref('')

// Computed
const totalBOMs = computed(() => boms.value.length)
const uniqueProducts = computed(() => {
  const productIds = new Set(boms.value.map(bom => bom.product_id))
  return productIds.size
})
const uniqueMaterials = computed(() => {
  const materialIds = new Set(boms.value.map(bom => bom.material_id))
  return materialIds.size
})

// Methods
const loadBOMs = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      product_id: productFilter.value,
      material_id: materialFilter.value,
      sort_field: sortField.value,
      sort_order: sortOrder.value
    }
    
    const response = await bomService.getBOMs(params)
    
    if (response.success) {
      boms.value = response.data.boms || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách BOM:', error)
    ElMessage.error('Không thể tải danh sách BOM')
  } finally {
    loading.value = false
  }
}

const loadProducts = async () => {
  try {
    const response = await productService.getProducts({ limit: 1000 })
    if (response.success) {
      products.value = response.data.products || []
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách sản phẩm:', error)
  }
}

const loadMaterials = async () => {
  try {
    const response = await materialService.getMaterials({ limit: 1000 })
    if (response.success) {
      materials.value = response.data.materials || []
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách nguyên vật liệu:', error)
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadBOMs()
}

const resetFilters = () => {
  productFilter.value = ''
  materialFilter.value = ''
  sortField.value = ''
  sortOrder.value = ''
  currentPage.value = 1
  loadBOMs()
}

const handleSortChange = ({ prop, order }) => {
  sortField.value = prop
  sortOrder.value = order === 'ascending' ? 'asc' : 'desc'
  loadBOMs()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadBOMs()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadBOMs()
}

const editBOM = (bom) => {
  selectedBOM.value = { ...bom }
  showCreateDialog.value = true
}

const deleteBOM = async (bom) => {
  try {
    await ElMessageBox.confirm(
      `Bạn có chắc chắn muốn xóa BOM này?`,
      'Xác nhận xóa',
      {
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy',
        type: 'warning',
      }
    )
    
    const response = await bomService.deleteBOM(bom.bom_id)
    
    if (response.success) {
      ElMessage.success('Xóa BOM thành công')
      loadBOMs()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Lỗi khi xóa BOM:', error)
      ElMessage.error('Không thể xóa BOM')
    }
  }
}

const handleFormSuccess = () => {
  showCreateDialog.value = false
  selectedBOM.value = null
  loadBOMs()
}

const calculateMaterials = () => {
  ElMessage.info('Tính năng tính toán nguyên liệu đang được phát triển')
}

const checkAvailability = () => {
  ElMessage.info('Tính năng kiểm tra tồn kho đang được phát triển')
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount)
}

const getColorCode = (colorName) => {
  const colorMap = {
    'Đỏ': '#f56c6c',
    'Xanh': '#409eff',
    'Vàng': '#e6a23c',
    'Xanh lá': '#67c23a',
    'Tím': '#9c27b0',
    'Cam': '#ff9800',
    'Hồng': '#e91e63',
    'Nâu': '#795548',
    'Xám': '#9e9e9e',
    'Đen': '#424242',
    'Trắng': '#ffffff'
  }
  return colorMap[colorName] || '#909399'
}

// Lifecycle
onMounted(() => {
  loadBOMs()
  loadProducts()
  loadMaterials()
})
</script>

<style scoped>
.bom-list {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 16px;
}

.table-card {
  margin-bottom: 24px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.product-info {
  line-height: 1.4;
}

.product-details {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.weight {
  font-size: 12px;
  color: #909399;
}

.material-info {
  line-height: 1.4;
}

.supplier {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.quantity {
  font-weight: 600;
  color: #303133;
}

.stats-row {
  margin-bottom: 24px;
}

.stats-content {
  padding: 16px 0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.stat-value {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.quick-actions {
  padding: 16px 0;
}

/* Responsive */
@media (max-width: 768px) {
  .bom-list {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-right {
    width: 100%;
  }
}
</style>
