<template>
  <div class="api-test">
    <div class="page-header">
      <h2>API Connection Test</h2>
      <p>Test kết nối và tương tác với backend API</p>
    </div>

    <!-- Connection Status -->
    <el-card class="status-card">
      <template #header>
        <h3>Trạng thái kết nối</h3>
      </template>
      <div class="connection-status">
        <div class="status-item">
          <span class="status-label">Backend URL:</span>
          <span class="status-value">{{ backendUrl }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">Trạng thái:</span>
          <el-tag :type="connectionStatus.type">{{ connectionStatus.text }}</el-tag>
        </div>
        <div class="status-item">
          <span class="status-label">Thời gian test cuối:</span>
          <span class="status-value">{{ lastTestTime || 'Chưa test' }}</span>
        </div>
      </div>
      <div class="status-actions">
        <el-button 
          type="primary" 
          @click="testConnection"
          :loading="testing"
        >
          Test Connection
        </el-button>
      </div>
    </el-card>

    <!-- API Tests -->
    <el-row :gutter="20">
      <!-- Product API Tests -->
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <h3>Product API Tests</h3>
          </template>
          
          <div class="test-section">
            <h4>GET /api/v1/product</h4>
            <el-button 
              @click="testGetProducts" 
              :loading="loadingProducts"
              size="small"
            >
              Test Get Products
            </el-button>
            <div v-if="productsResult" class="test-result">
              <el-tag :type="productsResult.success ? 'success' : 'danger'">
                {{ productsResult.success ? 'Success' : 'Failed' }}
              </el-tag>
              <div class="result-details">
                <pre>{{ JSON.stringify(productsResult, null, 2) }}</pre>
              </div>
            </div>
          </div>

          <el-divider />

          <div class="test-section">
            <h4>POST /api/v1/product</h4>
            <el-form :model="testProduct" size="small">
              <el-form-item label="Product Name">
                <el-input v-model="testProduct.productName" />
              </el-form-item>
              <el-form-item label="Weight">
                <el-input-number v-model="testProduct.productWeight" />
              </el-form-item>
              <el-form-item label="Injection Time">
                <el-input-number v-model="testProduct.injectionTime" />
              </el-form-item>
              <el-form-item label="Color">
                <el-input v-model="testProduct.color" />
              </el-form-item>
              <el-form-item label="Unit Price">
                <el-input-number v-model="testProduct.unitPrice" />
              </el-form-item>
            </el-form>
            <el-button 
              @click="testCreateProduct" 
              :loading="creatingProduct"
              type="primary"
              size="small"
            >
              Test Create Product
            </el-button>
            <div v-if="createResult" class="test-result">
              <el-tag :type="createResult.success ? 'success' : 'danger'">
                {{ createResult.success ? 'Success' : 'Failed' }}
              </el-tag>
              <div class="result-details">
                <pre>{{ JSON.stringify(createResult, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- Raw API Tests -->
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <h3>Raw API Tests</h3>
          </template>
          
          <div class="test-section">
            <h4>Custom Request</h4>
            <el-form size="small">
              <el-form-item label="Method">
                <el-select v-model="customRequest.method">
                  <el-option label="GET" value="GET" />
                  <el-option label="POST" value="POST" />
                  <el-option label="PUT" value="PUT" />
                  <el-option label="DELETE" value="DELETE" />
                </el-select>
              </el-form-item>
              <el-form-item label="Endpoint">
                <el-input v-model="customRequest.endpoint" placeholder="/api/v1/product" />
              </el-form-item>
              <el-form-item label="Body (JSON)" v-if="['POST', 'PUT'].includes(customRequest.method)">
                <el-input 
                  v-model="customRequest.body" 
                  type="textarea" 
                  :rows="4"
                  placeholder='{"productName": "Test Product"}'
                />
              </el-form-item>
            </el-form>
            <el-button 
              @click="testCustomRequest" 
              :loading="customLoading"
              type="primary"
              size="small"
            >
              Send Request
            </el-button>
            <div v-if="customResult" class="test-result">
              <el-tag :type="customResult.success ? 'success' : 'danger'">
                {{ customResult.success ? 'Success' : 'Failed' }}
              </el-tag>
              <div class="result-details">
                <pre>{{ JSON.stringify(customResult, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Console Logs -->
    <el-card class="logs-card">
      <template #header>
        <div class="logs-header">
          <h3>Console Logs</h3>
          <el-button @click="clearLogs" size="small">Clear</el-button>
        </div>
      </template>
      <div class="logs-content">
        <div 
          v-for="(log, index) in logs" 
          :key="index" 
          class="log-item"
          :class="log.type"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        <div v-if="logs.length === 0" class="no-logs">
          Chưa có logs. Thực hiện test để xem kết quả.
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { productService } from '@/services'

// Reactive data
const testing = ref(false)
const lastTestTime = ref('')
const connectionStatus = ref({ type: 'info', text: 'Chưa test' })

const loadingProducts = ref(false)
const productsResult = ref(null)

const creatingProduct = ref(false)
const createResult = ref(null)

const customLoading = ref(false)
const customResult = ref(null)

const logs = ref([])

// Test data
const testProduct = reactive({
  productName: 'Test Product API',
  productWeight: 350.5,
  injectionTime: 12.3,
  color: 'Red',
  unitPrice: 79.99
})

const customRequest = reactive({
  method: 'GET',
  endpoint: '/api/v1/product',
  body: ''
})

// Computed
const backendUrl = computed(() => {
  return process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080/api/v1'
})

// Methods
const addLog = (message, type = 'info') => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
  
  // Keep only last 50 logs
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

const testConnection = async () => {
  try {
    testing.value = true
    addLog('Testing connection...', 'info')
    
    const response = await fetch(`${backendUrl.value}/product`)
    const data = await response.json()
    
    if (response.ok) {
      connectionStatus.value = { type: 'success', text: 'Kết nối thành công' }
      addLog(`Connection successful: ${JSON.stringify(data)}`, 'success')
    } else {
      connectionStatus.value = { type: 'danger', text: 'Kết nối thất bại' }
      addLog(`Connection failed: ${JSON.stringify(data)}`, 'error')
    }
    
    lastTestTime.value = new Date().toLocaleString()
  } catch (error) {
    connectionStatus.value = { type: 'danger', text: 'Lỗi kết nối' }
    addLog(`Connection error: ${error.message}`, 'error')
    lastTestTime.value = new Date().toLocaleString()
  } finally {
    testing.value = false
  }
}

const testGetProducts = async () => {
  try {
    loadingProducts.value = true
    addLog('Testing GET /api/v1/product...', 'info')
    
    const response = await productService.getProducts()
    productsResult.value = response
    
    if (response.success) {
      addLog(`GET Products successful: ${response.data?.length || 0} products found`, 'success')
    } else {
      addLog(`GET Products failed: ${response.message}`, 'error')
    }
  } catch (error) {
    productsResult.value = { success: false, error: error.message }
    addLog(`GET Products error: ${error.message}`, 'error')
  } finally {
    loadingProducts.value = false
  }
}

const testCreateProduct = async () => {
  try {
    creatingProduct.value = true
    addLog('Testing POST /api/v1/product...', 'info')
    
    const response = await productService.createProduct(testProduct)
    createResult.value = response
    
    if (response.success) {
      addLog('POST Product successful', 'success')
      ElMessage.success('Product created successfully!')
    } else {
      addLog(`POST Product failed: ${response.message}`, 'error')
    }
  } catch (error) {
    createResult.value = { success: false, error: error.message }
    addLog(`POST Product error: ${error.message}`, 'error')
  } finally {
    creatingProduct.value = false
  }
}

const testCustomRequest = async () => {
  try {
    customLoading.value = true
    addLog(`Testing ${customRequest.method} ${customRequest.endpoint}...`, 'info')
    
    const options = {
      method: customRequest.method,
      headers: {
        'Content-Type': 'application/json'
      }
    }
    
    if (['POST', 'PUT'].includes(customRequest.method) && customRequest.body) {
      options.body = customRequest.body
    }
    
    const response = await fetch(`${backendUrl.value}${customRequest.endpoint}`, options)
    const data = await response.json()
    
    customResult.value = {
      success: response.ok,
      status: response.status,
      data: data
    }
    
    if (response.ok) {
      addLog(`${customRequest.method} request successful`, 'success')
    } else {
      addLog(`${customRequest.method} request failed: ${response.status}`, 'error')
    }
  } catch (error) {
    customResult.value = { success: false, error: error.message }
    addLog(`${customRequest.method} request error: ${error.message}`, 'error')
  } finally {
    customLoading.value = false
  }
}

const clearLogs = () => {
  logs.value = []
}
</script>

<style scoped>
.api-test {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.status-card {
  margin-bottom: 24px;
}

.connection-status {
  margin-bottom: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.status-label {
  font-weight: 500;
  color: #606266;
}

.status-value {
  color: #303133;
}

.test-section {
  margin-bottom: 20px;
}

.test-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}

.test-result {
  margin-top: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.result-details {
  margin-top: 8px;
}

.result-details pre {
  font-size: 12px;
  color: #606266;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.logs-card {
  margin-top: 24px;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logs-header h3 {
  margin: 0;
}

.logs-content {
  max-height: 300px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  padding: 4px 8px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  gap: 12px;
}

.log-item.success {
  background: #f0f9ff;
  color: #059669;
}

.log-item.error {
  background: #fef2f2;
  color: #dc2626;
}

.log-item.info {
  background: #f8fafc;
  color: #475569;
}

.log-time {
  color: #6b7280;
  min-width: 80px;
}

.log-message {
  flex: 1;
  word-break: break-word;
}

.no-logs {
  padding: 20px;
  text-align: center;
  color: #9ca3af;
}

/* Mobile styles */
@media (max-width: 768px) {
  .api-test {
    padding: 16px;
  }
  
  .status-item {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
