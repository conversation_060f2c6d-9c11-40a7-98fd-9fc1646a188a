<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <el-image
            style="width: 60px; height: 60px"
            src="/public/product-management.png"
            fit="cover"
          />
        </div>
        <h1>Hệ thống ERP</h1>
        <p><PERSON><PERSON><PERSON><PERSON> l<PERSON> tr<PERSON> xu<PERSON></p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="Tên đăng nhập"
            size="large"
            :prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="Mật khẩu"
            size="large"
            :prefix-icon="Lock"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="loginForm.remember">
            <PERSON>hi nhớ đăng nhập
          </el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            style="width: 100%"
            :loading="loading"
            @click="handleLogin"
          >
            Đăng nhập
          </el-button>
        </el-form-item>
      </el-form>

      <div class="login-footer">
        <p>Demo Account:</p>
        <p><strong>Username:</strong> admin</p>
        <p><strong>Password:</strong> admin123</p>
      </div>
    </div>

    <!-- Background decoration -->
    <div class="background-decoration">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { authService } from '@/services'

// Router
const router = useRouter()

// Refs
const loginFormRef = ref()
const loading = ref(false)

// Form data
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// Validation rules
const loginRules = {
  username: [
    { required: true, message: 'Vui lòng nhập tên đăng nhập', trigger: 'blur' },
    { min: 3, max: 50, message: 'Tên đăng nhập phải từ 3-50 ký tự', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Vui lòng nhập mật khẩu', trigger: 'blur' },
    { min: 6, max: 50, message: 'Mật khẩu phải từ 6-50 ký tự', trigger: 'blur' }
  ]
}

// Methods
const handleLogin = async () => {
  try {
    console.log('Login attempt:', loginForm.username, loginForm.password)

    // Validate form
    if (!loginFormRef.value) {
      console.error('Form ref not found')
      return
    }

    const valid = await loginFormRef.value.validate()
    if (!valid) {
      console.log('Form validation failed')
      return
    }

    loading.value = true
    console.log('Starting login process...')

    // Demo login - bypass API call
    if (loginForm.username === 'admin' && loginForm.password === 'admin123') {
      console.log('Demo login successful')

      // Mock successful login
      const mockToken = 'mock-jwt-token-' + Date.now()
      const mockUser = {
        id: 1,
        username: 'admin',
        name: 'Administrator',
        email: '<EMAIL>',
        role: 'admin',
        permissions: ['all']
      }

      // Save to localStorage
      localStorage.setItem('auth_token', mockToken)
      localStorage.setItem('user_info', JSON.stringify(mockUser))

      console.log('Token saved:', mockToken)
      console.log('User saved:', mockUser)

      ElMessage.success('Đăng nhập thành công!')

      // Small delay before redirect
      setTimeout(() => {
        console.log('Redirecting to dashboard...')
        router.push('/dashboard')
      }, 500)
    } else {
      console.log('Invalid credentials')
      ElMessage.error('Tên đăng nhập hoặc mật khẩu không đúng')
    }
  } catch (error) {
    console.error('Lỗi đăng nhập:', error)
    ElMessage.error('Đăng nhập thất bại. Vui lòng thử lại.')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.login-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  position: relative;
  z-index: 10;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  margin-bottom: 16px;
}

.login-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.login-header p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.login-form {
  margin-bottom: 24px;
}

.login-form .el-form-item {
  margin-bottom: 20px;
}

.login-footer {
  text-align: center;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 8px;
  font-size: 14px;
  color: #606266;
}

.login-footer p {
  margin: 4px 0;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.shape-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .login-container {
    padding: 20px;
  }
  
  .login-card {
    padding: 24px;
  }
  
  .login-header h1 {
    font-size: 24px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: #1f1f1f;
    color: #ffffff;
  }
  
  .login-header h1 {
    color: #ffffff;
  }
  
  .login-header p {
    color: #cccccc;
  }
  
  .login-footer {
    background: #2a2a2a;
    color: #cccccc;
  }
}
</style>
