<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>Dashboard - Tổng quan hệ thống</h1>
      <p>Ch<PERSON>o mừng bạn đến với hệ thống ERP quản lý quy trình sản xuất</p>
    </div>

    <!-- Statistics Cards -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon materials">
              <el-icon><Box /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ materialCount }}</h3>
              <p>Nguyên vật liệu</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon products">
              <el-icon><ShoppingBag /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ productCount }}</h3>
              <p>Sản phẩm</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon orders">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ orderCount }}</h3>
              <p>Đơn hàng</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon inventory">
              <el-icon><Goods /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ inventoryValue }}</h3>
              <p>Giá trị tồn kho</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Quick Actions -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <h3>Thao tác nhanh</h3>
          </template>
          <div class="action-buttons">
            <el-button 
              type="primary" 
              @click="$router.push('/materials')"
              class="action-btn"
            >
              <el-icon><Plus /></el-icon>
              Thêm nguyên vật liệu
            </el-button>
            <el-button 
              type="success" 
              @click="$router.push('/products')"
              class="action-btn"
            >
              <el-icon><Plus /></el-icon>
              Thêm sản phẩm
            </el-button>
            <el-button 
              type="warning" 
              @click="$router.push('/orders/create')"
              class="action-btn"
            >
              <el-icon><Document /></el-icon>
              Tạo đơn hàng
            </el-button>
            <el-button 
              type="info" 
              @click="$router.push('/bom')"
              class="action-btn"
            >
              <el-icon><Connection /></el-icon>
              Quản lý BOM
            </el-button>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <h3>Hoạt động gần đây</h3>
          </template>
          <div class="recent-activities">
            <div 
              v-for="activity in recentActivities" 
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="activity-content">
                <p class="activity-text">{{ activity.description }}</p>
                <span class="activity-time">{{ activity.time }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- System Status -->
    <el-row :gutter="20" class="system-status">
      <el-col :span="24">
        <el-card>
          <template #header>
            <h3>Trạng thái hệ thống</h3>
          </template>
          <el-row :gutter="20">
            <el-col :xs="24" :md="8">
              <div class="status-item">
                <el-icon class="status-icon success"><CircleCheck /></el-icon>
                <span>API Server: Hoạt động bình thường</span>
              </div>
            </el-col>
            <el-col :xs="24" :md="8">
              <div class="status-item">
                <el-icon class="status-icon success"><CircleCheck /></el-icon>
                <span>Database: Kết nối ổn định</span>
              </div>
            </el-col>
            <el-col :xs="24" :md="8">
              <div class="status-item">
                <el-icon class="status-icon warning"><Warning /></el-icon>
                <span>Backup: Cần cập nhật</span>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- Quick Actions -->
    <QuickActions @quick-action="handleQuickAction" />

    <!-- Inventory Wizard -->
    <InventoryWizard
      v-model:visible="showInventoryWizard"
      :initial-type="wizardConfig.type"
      :initial-direction="wizardConfig.direction"
      @success="handleWizardSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Box,
  ShoppingBag,
  Document,
  Goods,
  Plus,
  Connection,
  Clock,
  CircleCheck,
  Warning
} from '@element-plus/icons-vue'
import QuickActions from '@/components/common/QuickActions.vue'
import InventoryWizard from '@/components/common/InventoryWizard.vue'

// Reactive data
const materialCount = ref(0)
const productCount = ref(0)
const orderCount = ref(0)
const inventoryValue = ref('0 VNĐ')
const showInventoryWizard = ref(false)

const wizardConfig = reactive({
  type: 'material',
  direction: 'IN'
})

const recentActivities = ref([
  {
    id: 1,
    description: 'Thêm mới nguyên vật liệu: Nhựa PVC',
    time: '2 phút trước'
  },
  {
    id: 2,
    description: 'Cập nhật đơn hàng #DH001',
    time: '15 phút trước'
  },
  {
    id: 3,
    description: 'Nhập kho sản phẩm: Ống nhựa 20mm',
    time: '1 giờ trước'
  },
  {
    id: 4,
    description: 'Tạo BOM cho sản phẩm mới',
    time: '2 giờ trước'
  }
])

// Methods
const loadDashboardData = async () => {
  try {
    // TODO: Gọi API để lấy dữ liệu thống kê
    // Hiện tại sử dụng dữ liệu giả lập
    materialCount.value = 25
    productCount.value = 18
    orderCount.value = 12
    inventoryValue.value = '2.5 tỷ VNĐ'
  } catch (error) {
    console.error('Lỗi khi tải dữ liệu dashboard:', error)
  }
}

// Methods
const handleQuickAction = (action) => {
  switch (action.type) {
    case 'add-material':
      // Trigger material form dialog
      ElMessage.info('Mở form thêm nguyên vật liệu')
      break
    case 'material-inbound':
      wizardConfig.type = 'material'
      wizardConfig.direction = 'IN'
      showInventoryWizard.value = true
      break
    case 'material-outbound':
      wizardConfig.type = 'material'
      wizardConfig.direction = 'OUT'
      showInventoryWizard.value = true
      break
    case 'add-product':
      ElMessage.info('Mở form thêm sản phẩm')
      break
    case 'product-inbound':
      wizardConfig.type = 'product'
      wizardConfig.direction = 'IN'
      showInventoryWizard.value = true
      break
    default:
      ElMessage.info(`Thao tác ${action.type} đang được phát triển`)
  }
}

const handleWizardSuccess = () => {
  loadDashboardData() // Refresh data after successful transaction
}

// Lifecycle
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 24px;
}

.dashboard-header {
  margin-bottom: 24px;
}

.dashboard-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.dashboard-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 24px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: #fff;
}

.stats-icon.materials {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.products {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.orders {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.inventory {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info h3 {
  margin: 0 0 4px 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.stats-info p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.quick-actions {
  margin-bottom: 24px;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.action-btn {
  width: 100%;
  height: 48px;
}

.recent-activities {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #909399;
}

.activity-content {
  flex: 1;
}

.activity-text {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #303133;
}

.activity-time {
  font-size: 12px;
  color: #909399;
}

.system-status {
  margin-bottom: 24px;
}

.status-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.status-icon {
  margin-right: 8px;
  font-size: 16px;
}

.status-icon.success {
  color: #67c23a;
}

.status-icon.warning {
  color: #e6a23c;
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }
  
  .action-buttons {
    grid-template-columns: 1fr;
  }
}
</style>
