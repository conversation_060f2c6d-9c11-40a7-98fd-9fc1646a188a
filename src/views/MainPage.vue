<template>
  <section class="contaner">
    <CustomHeader></CustomHeader>
    <CustomMain></CustomMain>
  </section>
</template>

<script setup lang="ts">
import CustomHeader from "@/components/layout/CustomHeader.vue";
import CustomMain from "@/components/layout/CustomMain.vue";
</script>

<style scoped>
.contaner {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background-color: #212035;
}
</style>
