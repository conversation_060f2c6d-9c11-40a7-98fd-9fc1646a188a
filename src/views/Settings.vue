<template>
  <div class="settings">
    <div class="page-header">
      <div class="header-left">
        <h2><PERSON><PERSON><PERSON> đặt Hệ thống</h2>
        <p><PERSON><PERSON><PERSON> hình và tùy chỉnh hệ thống ERP</p>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- Cài đặt chung -->
      <el-col :xs="24" :lg="12">
        <el-card>
          <template #header>
            <h3>Cài đặt chung</h3>
          </template>
          <el-form label-width="140px">
            <el-form-item label="Tên công ty">
              <el-input v-model="settings.companyName" placeholder="Nhập tên công ty" />
            </el-form-item>
            <el-form-item label="Địa chỉ">
              <el-input 
                v-model="settings.address" 
                type="textarea" 
                :rows="2"
                placeholder="Nhập địa chỉ công ty" 
              />
            </el-form-item>
            <el-form-item label="Số điện thoại">
              <el-input v-model="settings.phone" placeholder="Nhập số điện thoại" />
            </el-form-item>
            <el-form-item label="Email">
              <el-input v-model="settings.email" placeholder="Nhập email" />
            </el-form-item>
            <el-form-item label="Múi giờ">
              <el-select v-model="settings.timezone" style="width: 100%">
                <el-option label="GMT+7 (Việt Nam)" value="Asia/Ho_Chi_Minh" />
                <el-option label="GMT+0 (UTC)" value="UTC" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- Cài đặt tồn kho -->
      <el-col :xs="24" :lg="12">
        <el-card>
          <template #header>
            <h3>Cài đặt tồn kho</h3>
          </template>
          <el-form label-width="140px">
            <el-form-item label="Ngưỡng cảnh báo">
              <el-input-number 
                v-model="settings.lowStockThreshold" 
                :min="0"
                style="width: 100%"
              />
              <div class="form-help">Cảnh báo khi tồn kho dưới ngưỡng này</div>
            </el-form-item>
            <el-form-item label="Tự động đặt hàng">
              <el-switch v-model="settings.autoReorder" />
              <div class="form-help">Tự động tạo đơn đặt hàng khi hết tồn kho</div>
            </el-form-item>
            <el-form-item label="Đơn vị tiền tệ">
              <el-select v-model="settings.currency" style="width: 100%">
                <el-option label="VNĐ (Việt Nam Đồng)" value="VND" />
                <el-option label="USD (US Dollar)" value="USD" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- Cài đặt thông báo -->
      <el-col :xs="24" :lg="12">
        <el-card>
          <template #header>
            <h3>Cài đặt thông báo</h3>
          </template>
          <el-form label-width="140px">
            <el-form-item label="Email thông báo">
              <el-switch v-model="settings.emailNotifications" />
              <div class="form-help">Gửi thông báo qua email</div>
            </el-form-item>
            <el-form-item label="Thông báo tồn kho">
              <el-switch v-model="settings.stockNotifications" />
              <div class="form-help">Thông báo khi tồn kho thấp</div>
            </el-form-item>
            <el-form-item label="Thông báo đơn hàng">
              <el-switch v-model="settings.orderNotifications" />
              <div class="form-help">Thông báo về trạng thái đơn hàng</div>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- Cài đặt bảo mật -->
      <el-col :xs="24" :lg="12">
        <el-card>
          <template #header>
            <h3>Cài đặt bảo mật</h3>
          </template>
          <el-form label-width="140px">
            <el-form-item label="Thời gian phiên">
              <el-select v-model="settings.sessionTimeout" style="width: 100%">
                <el-option label="30 phút" value="30" />
                <el-option label="1 giờ" value="60" />
                <el-option label="2 giờ" value="120" />
                <el-option label="4 giờ" value="240" />
              </el-select>
              <div class="form-help">Thời gian tự động đăng xuất</div>
            </el-form-item>
            <el-form-item label="Xác thực 2 lớp">
              <el-switch v-model="settings.twoFactorAuth" />
              <div class="form-help">Bật xác thực 2 lớp cho bảo mật</div>
            </el-form-item>
            <el-form-item label="Ghi log hoạt động">
              <el-switch v-model="settings.activityLogging" />
              <div class="form-help">Ghi lại các hoạt động của người dùng</div>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- Cài đặt sao lưu -->
      <el-col :xs="24" :lg="12">
        <el-card>
          <template #header>
            <h3>Sao lưu & Khôi phục</h3>
          </template>
          <div class="backup-section">
            <div class="backup-item">
              <div class="backup-info">
                <h4>Sao lưu tự động</h4>
                <p>Sao lưu dữ liệu hàng ngày lúc 2:00 AM</p>
              </div>
              <el-switch v-model="settings.autoBackup" />
            </div>
            <div class="backup-item">
              <div class="backup-info">
                <h4>Sao lưu thủ công</h4>
                <p>Tạo bản sao lưu ngay bây giờ</p>
              </div>
              <el-button type="primary" @click="createBackup">
                Tạo sao lưu
              </el-button>
            </div>
            <div class="backup-item">
              <div class="backup-info">
                <h4>Khôi phục dữ liệu</h4>
                <p>Khôi phục từ bản sao lưu</p>
              </div>
              <el-button type="warning" @click="restoreBackup">
                Khôi phục
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- Thông tin hệ thống -->
      <el-col :xs="24" :lg="12">
        <el-card>
          <template #header>
            <h3>Thông tin hệ thống</h3>
          </template>
          <div class="system-info">
            <div class="info-item">
              <span class="info-label">Phiên bản:</span>
              <span class="info-value">v1.0.0</span>
            </div>
            <div class="info-item">
              <span class="info-label">Cơ sở dữ liệu:</span>
              <span class="info-value">MySQL 8.0</span>
            </div>
            <div class="info-item">
              <span class="info-label">Server:</span>
              <span class="info-value">Node.js 18.x</span>
            </div>
            <div class="info-item">
              <span class="info-label">Cập nhật cuối:</span>
              <span class="info-value">{{ lastUpdate }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Trạng thái:</span>
              <el-tag type="success">Hoạt động bình thường</el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Nút lưu -->
    <div class="save-section">
      <el-button type="primary" size="large" @click="saveSettings" :loading="saving">
        Lưu cài đặt
      </el-button>
      <el-button size="large" @click="resetSettings">
        Đặt lại mặc định
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Reactive data
const saving = ref(false)
const lastUpdate = ref('17/06/2025 10:30:00')

const settings = reactive({
  // Cài đặt chung
  companyName: 'Công ty TNHH Sản xuất ABC',
  address: '123 Đường ABC, Quận 1, TP.HCM',
  phone: '0123456789',
  email: '<EMAIL>',
  timezone: 'Asia/Ho_Chi_Minh',
  
  // Cài đặt tồn kho
  lowStockThreshold: 10,
  autoReorder: false,
  currency: 'VND',
  
  // Cài đặt thông báo
  emailNotifications: true,
  stockNotifications: true,
  orderNotifications: true,
  
  // Cài đặt bảo mật
  sessionTimeout: '60',
  twoFactorAuth: false,
  activityLogging: true,
  
  // Sao lưu
  autoBackup: true
})

// Methods
const saveSettings = async () => {
  try {
    saving.value = true
    
    // TODO: Call API to save settings
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('Lưu cài đặt thành công')
  } catch (error) {
    console.error('Lỗi khi lưu cài đặt:', error)
    ElMessage.error('Không thể lưu cài đặt')
  } finally {
    saving.value = false
  }
}

const resetSettings = async () => {
  try {
    await ElMessageBox.confirm(
      'Bạn có chắc chắn muốn đặt lại tất cả cài đặt về mặc định?',
      'Xác nhận đặt lại',
      {
        confirmButtonText: 'Đặt lại',
        cancelButtonText: 'Hủy',
        type: 'warning',
      }
    )
    
    // Reset to default values
    Object.assign(settings, {
      companyName: '',
      address: '',
      phone: '',
      email: '',
      timezone: 'Asia/Ho_Chi_Minh',
      lowStockThreshold: 10,
      autoReorder: false,
      currency: 'VND',
      emailNotifications: true,
      stockNotifications: true,
      orderNotifications: true,
      sessionTimeout: '60',
      twoFactorAuth: false,
      activityLogging: true,
      autoBackup: true
    })
    
    ElMessage.success('Đã đặt lại cài đặt về mặc định')
  } catch (error) {
    // User cancelled
  }
}

const createBackup = () => {
  ElMessage.success('Đang tạo bản sao lưu...')
  // TODO: Implement backup functionality
}

const restoreBackup = () => {
  ElMessage.info('Tính năng khôi phục đang được phát triển')
  // TODO: Implement restore functionality
}
</script>

<style scoped>
.settings {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.backup-section {
  padding: 16px 0;
}

.backup-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.backup-item:last-child {
  border-bottom: none;
}

.backup-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 14px;
}

.backup-info p {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

.system-info {
  padding: 16px 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: #606266;
  font-size: 14px;
}

.info-value {
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.save-section {
  margin-top: 32px;
  text-align: center;
  padding: 24px;
  border-top: 1px solid #f0f0f0;
}

.save-section .el-button {
  margin: 0 8px;
}

/* Responsive */
@media (max-width: 768px) {
  .settings {
    padding: 16px;
  }
  
  .backup-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
