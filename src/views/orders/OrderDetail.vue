<template>
  <div class="order-detail">
    <div class="page-header">
      <div class="header-left">
        <h2>Chi tiết đơn hàng #{{ $route.params.id }}</h2>
        <p>Thông tin chi tiết và trạng thái đơn hàng</p>
      </div>
      <div class="header-right">
        <el-button @click="$router.push('/orders')">
          Quay lại danh sách
        </el-button>
      </div>
    </div>

    <el-card>
      <div class="coming-soon">
        <el-icon size="64"><View /></el-icon>
        <h3>Tính năng đang được phát triển</h3>
        <p>Trang chi tiết đơn hàng sẽ sớm được hoàn thiện</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { View } from '@element-plus/icons-vue'
</script>

<style scoped>
.order-detail {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon h3 {
  margin: 16px 0 8px 0;
  color: #606266;
}
</style>
