<template>
  <div class="order-list">
    <div class="page-header">
      <div class="header-left">
        <h2>Quản lý Đơn hàng</h2>
        <p><PERSON>h sách tất cả đơn hàng trong hệ thống</p>
      </div>
      <div class="header-right">
        <el-button 
          type="primary" 
          @click="$router.push('/orders/create')"
          :icon="Plus"
        >
          Tạo đơn hàng
        </el-button>
      </div>
    </div>

    <el-card>
      <div class="coming-soon">
        <el-icon size="64"><Document /></el-icon>
        <h3>Tính năng đang được phát triển</h3>
        <p>Module quản lý đơn hàng sẽ sớm được hoàn thiện</p>
        <div class="features-list">
          <ul>
            <li>✓ Tạo và quản lý đơn hàng</li>
            <li>✓ Theo dõi trạng thái đơn hàng</li>
            <li>✓ Quản lý chi tiết đơn hàng</li>
            <li>✓ Tính toán nguyên liệu cần thiết</li>
            <li>✓ Báo cáo đơn hàng</li>
          </ul>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Plus, Document } from '@element-plus/icons-vue'
</script>

<style scoped>
.order-list {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon h3 {
  margin: 16px 0 8px 0;
  color: #606266;
}

.features-list {
  margin-top: 24px;
}

.features-list ul {
  list-style: none;
  padding: 0;
  text-align: left;
  display: inline-block;
}

.features-list li {
  padding: 4px 0;
  color: #67c23a;
}
</style>
