<template>
  <div class="inventory-report">
    <div class="page-header">
      <div class="header-left">
        <h2><PERSON><PERSON><PERSON> c<PERSON>o <PERSON>ồn kho</h2>
        <p><PERSON><PERSON>o cáo chi tiết về tình trạng tồn kho nguyên vật liệu và sản phẩm</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Download">
          Xuất báo cáo
        </el-button>
      </div>
    </div>

    <!-- B<PERSON> lọc thời gian -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="đến"
            start-placeholder="Từ ngày"
            end-placeholder="Đến ngày"
            format="DD/MM/YYYY"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <el-select
            v-model="reportType"
            placeholder="Loại báo cáo"
            style="width: 100%"
          >
            <el-option label="Tồn kho nguyên vật liệu" value="materials" />
            <el-option label="Tồn kho sản phẩm" value="products" />
            <el-option label="Tổng hợp" value="summary" />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <el-button type="primary" @click="generateReport">
            Tạo báo cáo
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- Thống kê tổng quan -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon materials">
              <el-icon><Box /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ materialStats.total }}</h3>
              <p>Loại nguyên vật liệu</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon products">
              <el-icon><ShoppingBag /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ productStats.total }}</h3>
              <p>Loại sản phẩm</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon value">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ formatCurrency(totalValue) }}</h3>
              <p>Tổng giá trị tồn kho</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon warning">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ lowStockItems }}</h3>
              <p>Mặt hàng sắp hết</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Biểu đồ -->
    <el-row :gutter="20" class="charts-row">
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <h3>Phân bố giá trị tồn kho</h3>
          </template>
          <div class="chart-placeholder">
            <el-icon size="48"><PieChart /></el-icon>
            <p>Biểu đồ tròn phân bố giá trị tồn kho</p>
            <small>Tính năng biểu đồ đang được phát triển</small>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <h3>Xu hướng tồn kho theo thời gian</h3>
          </template>
          <div class="chart-placeholder">
            <el-icon size="48"><TrendCharts /></el-icon>
            <p>Biểu đồ đường xu hướng tồn kho</p>
            <small>Tính năng biểu đồ đang được phát triển</small>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Bảng chi tiết -->
    <el-card class="table-card">
      <template #header>
        <h3>Chi tiết tồn kho</h3>
      </template>
      <div class="coming-soon">
        <el-icon size="48"><DataAnalysis /></el-icon>
        <h4>Bảng báo cáo chi tiết</h4>
        <p>Bảng dữ liệu chi tiết về tồn kho sẽ được hiển thị tại đây</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Download,
  Box,
  ShoppingBag,
  Money,
  Warning,
  PieChart,
  TrendCharts,
  DataAnalysis
} from '@element-plus/icons-vue'

// Reactive data
const dateRange = ref([])
const reportType = ref('summary')

// Mock data
const materialStats = reactive({
  total: 25,
  inStock: 20,
  lowStock: 3,
  outOfStock: 2
})

const productStats = reactive({
  total: 18,
  inStock: 15,
  lowStock: 2,
  outOfStock: 1
})

// Computed
const totalValue = computed(() => 2500000000) // 2.5 tỷ VNĐ
const lowStockItems = computed(() => materialStats.lowStock + productStats.lowStock)

// Methods
const generateReport = () => {
  ElMessage.success('Đang tạo báo cáo...')
  // TODO: Implement report generation
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount)
}
</script>

<style scoped>
.inventory-report {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 24px;
}

.stats-row {
  margin-bottom: 24px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: #fff;
}

.stats-icon.materials {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.products {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.value {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-icon.warning {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stats-info h3 {
  margin: 0 0 4px 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.stats-info p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.charts-row {
  margin-bottom: 24px;
}

.chart-placeholder {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.chart-placeholder p {
  margin: 12px 0 4px 0;
  color: #606266;
}

.chart-placeholder small {
  color: #c0c4cc;
}

.table-card {
  margin-bottom: 24px;
}

.coming-soon {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.coming-soon h4 {
  margin: 12px 0 8px 0;
  color: #606266;
}

/* Responsive */
@media (max-width: 768px) {
  .inventory-report {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
}
</style>
