<template>
  <div class="order-report">
    <div class="page-header">
      <div class="header-left">
        <h2><PERSON><PERSON><PERSON> cáo Đơn hàng</h2>
        <p>B<PERSON>o cáo tình trạng và hiệu suất thực hiện đơn hàng</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Download">
          Xuất báo cáo
        </el-button>
      </div>
    </div>

    <el-card>
      <div class="coming-soon">
        <el-icon size="64"><DataLine /></el-icon>
        <h3>Tính năng đang được phát triển</h3>
        <p>Báo cáo đơn hàng sẽ sớm được hoàn thiện</p>
        <div class="features-list">
          <ul>
            <li>✓ Thống kê đơn hàng theo trạng thái</li>
            <li>✓ Doanh thu theo thời gian</li>
            <li>✓ Tỷ lệ hoàn thành đơn hàng</li>
            <li>✓ Thời gian giao hàng trung bình</li>
            <li>✓ Phân tích khách hàng</li>
          </ul>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Download, DataLine } from '@element-plus/icons-vue'
</script>

<style scoped>
.order-report {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon h3 {
  margin: 16px 0 8px 0;
  color: #606266;
}

.features-list {
  margin-top: 24px;
}

.features-list ul {
  list-style: none;
  padding: 0;
  text-align: left;
  display: inline-block;
}

.features-list li {
  padding: 4px 0;
  color: #67c23a;
}
</style>
