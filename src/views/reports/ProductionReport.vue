<template>
  <div class="production-report">
    <div class="page-header">
      <div class="header-left">
        <h2><PERSON><PERSON>o c<PERSON>o <PERSON>ản xuất</h2>
        <p>B<PERSON>o cáo hiệu suất sản xuất và sử dụng nguyên vật liệu</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Download">
          Xuất báo cáo
        </el-button>
      </div>
    </div>

    <el-card>
      <div class="coming-soon">
        <el-icon size="64"><TrendCharts /></el-icon>
        <h3>Tính năng đang được phát triển</h3>
        <p>Báo cáo sản xuất sẽ sớm được hoàn thiện</p>
        <div class="features-list">
          <ul>
            <li>✓ Hiệu suất sản xuất theo thời gian</li>
            <li>✓ Tỷ lệ sử dụng nguyên vật liệu</li>
            <li>✓ Thời gian sản xuất trung bình</li>
            <li>✓ Phân tích chi phí sản xuất</li>
            <li>✓ Báo cáo chất lượng sản phẩm</li>
          </ul>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Download, TrendCharts } from '@element-plus/icons-vue'
</script>

<style scoped>
.production-report {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon h3 {
  margin: 16px 0 8px 0;
  color: #606266;
}

.features-list {
  margin-top: 24px;
}

.features-list ul {
  list-style: none;
  padding: 0;
  text-align: left;
  display: inline-block;
}

.features-list li {
  padding: 4px 0;
  color: #67c23a;
}
</style>
