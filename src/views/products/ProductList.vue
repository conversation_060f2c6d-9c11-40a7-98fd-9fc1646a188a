<template>
  <div class="product-list">
    <!-- Header v<PERSON><PERSON> nút thêm mới -->
    <div class="page-header">
      <div class="header-left">
        <h2>Quản lý Sản phẩm</h2>
        <p><PERSON><PERSON> sách tất cả sản phẩm trong hệ thống</p>
      </div>
      <div class="header-right">
        <el-button 
          type="primary" 
          @click="showCreateDialog = true"
          :icon="Plus"
        >
          Thêm sản phẩm
        </el-button>
      </div>
    </div>

    <!-- Bộ lọc và tìm kiếm -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <el-input
            v-model="searchQuery"
            placeholder="Tìm kiếm theo tên sản phẩm..."
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <el-input
            v-model="colorFilter"
            placeholder="Lọc theo màu sắc..."
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <el-button @click="resetFilters" :icon="Refresh">
            Đặt lại bộ lọc
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- Bảng dữ liệu -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="products"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column
          prop="product_id"
          label="ID"
          width="80"
          sortable="custom"
        />
        <el-table-column
          prop="product_name"
          label="Tên sản phẩm"
          min-width="200"
          sortable="custom"
        />
        <el-table-column
          prop="color"
          label="Màu sắc"
          width="100"
          sortable="custom"
        >
          <template #default="{ row }">
            <el-tag :color="getColorCode(row.color)" effect="dark">
              {{ row.color }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="product_weight_g"
          label="Trọng lượng (g)"
          width="130"
          sortable="custom"
        />
        <el-table-column
          prop="injection_time_s"
          label="Thời gian ép (s)"
          width="140"
          sortable="custom"
        />
        <el-table-column
          prop="unit_price"
          label="Giá đơn vị"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ formatCurrency(row.unit_price) }}
          </template>
        </el-table-column>
        <el-table-column
          label="Thao tác"
          width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="editProduct(row)"
              :icon="Edit"
            >
              Sửa
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteProduct(row)"
              :icon="Delete"
            >
              Xóa
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- Phân trang -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- Dialog thêm/sửa sản phẩm -->
    <ProductFormDialog
      v-model:visible="showCreateDialog"
      :product="selectedProduct"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Search, 
  Refresh, 
  Edit, 
  Delete 
} from '@element-plus/icons-vue'
import { productService } from '@/services'
import ProductFormDialog from '@/components/products/ProductFormDialog.vue'

// Reactive data
const loading = ref(false)
const products = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const searchQuery = ref('')
const colorFilter = ref('')
const showCreateDialog = ref(false)
const selectedProduct = ref(null)

// Sorting
const sortField = ref('')
const sortOrder = ref('')

// Methods
const loadProducts = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      color: colorFilter.value,
      sort_field: sortField.value,
      sort_order: sortOrder.value
    }

    const response = await productService.getProducts(params)
    console.log(response)

    if (response.success) {
      // Handle both array and object response formats
      if (Array.isArray(response.data)) {
        products.value = response.data
        total.value = response.data.length
      } else {
        products.value = response.data.products || response.data || []
        total.value = response.data.total || products.value.length
      }
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách sản phẩm:', error)
    ElMessage.error('Không thể tải danh sách sản phẩm')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadProducts()
}

const resetFilters = () => {
  searchQuery.value = ''
  colorFilter.value = ''
  sortField.value = ''
  sortOrder.value = ''
  currentPage.value = 1
  loadProducts()
}

const handleSortChange = ({ prop, order }) => {
  sortField.value = prop
  sortOrder.value = order === 'ascending' ? 'asc' : 'desc'
  loadProducts()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadProducts()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadProducts()
}

const editProduct = (product) => {
  selectedProduct.value = { ...product }
  showCreateDialog.value = true
}

const deleteProduct = async (product) => {
  try {
    await ElMessageBox.confirm(
      `Bạn có chắc chắn muốn xóa sản phẩm "${product.product_name}"?`,
      'Xác nhận xóa',
      {
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy',
        type: 'warning',
      }
    )
    
    const response = await productService.deleteProduct(product.product_id)
    
    if (response.success) {
      ElMessage.success('Xóa sản phẩm thành công')
      loadProducts()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Lỗi khi xóa sản phẩm:', error)
      ElMessage.error('Không thể xóa sản phẩm')
    }
  }
}

const handleFormSuccess = () => {
  showCreateDialog.value = false
  selectedProduct.value = null
  loadProducts()
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount)
}

const getColorCode = (colorName) => {
  const colorMap = {
    'Đỏ': '#f56c6c',
    'Xanh': '#409eff',
    'Vàng': '#e6a23c',
    'Xanh lá': '#67c23a',
    'Tím': '#9c27b0',
    'Cam': '#ff9800',
    'Hồng': '#e91e63',
    'Nâu': '#795548',
    'Xám': '#9e9e9e',
    'Đen': '#424242',
    'Trắng': '#ffffff'
  }
  return colorMap[colorName] || '#909399'
}

// Lifecycle
onMounted(() => {
  loadProducts()
})
</script>

<style scoped>
.product-list {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 16px;
}

.table-card {
  margin-bottom: 24px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* Responsive */
@media (max-width: 768px) {
  .product-list {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-right {
    width: 100%;
  }
  
  .header-right .el-button {
    width: 100%;
  }
}
</style>
