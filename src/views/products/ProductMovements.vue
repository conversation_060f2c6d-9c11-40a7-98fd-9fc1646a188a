<template>
  <div class="product-movements">
    <div class="page-header">
      <div class="header-left">
        <h2><PERSON><PERSON><PERSON><PERSON> k<PERSON>ập xuất <PERSON>ản phẩm</h2>
        <p><PERSON><PERSON> lịch sử nhập xu<PERSON>t của các sản phẩm</p>
      </div>
    </div>

    <el-card>
      <div class="coming-soon">
        <el-icon size="64"><TrendCharts /></el-icon>
        <h3>Tính năng đang được phát triển</h3>
        <p>Module nhật ký nhập xuất sản phẩm sẽ sớm được hoàn thiện</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { TrendCharts } from '@element-plus/icons-vue'
</script>

<style scoped>
.product-movements {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon h3 {
  margin: 16px 0 8px 0;
  color: #606266;
}
</style>
