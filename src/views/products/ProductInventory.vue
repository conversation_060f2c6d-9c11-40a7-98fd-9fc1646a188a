<template>
  <div class="product-inventory">
    <div class="page-header">
      <div class="header-left">
        <h2>T<PERSON><PERSON> kho <PERSON>ản phẩm</h2>
        <p>Theo dõi số lượng tồn kho của các sản phẩm</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus">
          <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> (Sản xuất)
        </el-button>
        <el-button type="warning" :icon="Minus">
          <PERSON><PERSON><PERSON> kho (Giao hàng)
        </el-button>
      </div>
    </div>

    <el-card>
      <div class="coming-soon">
        <el-icon size="64"><Box /></el-icon>
        <h3>Tính năng đang được phát triển</h3>
        <p><PERSON><PERSON><PERSON> tồn kho sản phẩm sẽ sớm được hoàn thiện</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Plus, Minus, Box } from '@element-plus/icons-vue'
</script>

<style scoped>
.product-inventory {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.coming-soon h3 {
  margin: 16px 0 8px 0;
  color: #606266;
}
</style>
