<template>
  <div class="test-login">
    <div class="test-card">
      <h2>Test Login - Bypass Authentication</h2>
      <p>Trang này để test nhanh hệ thống mà không cần đăng nhập</p>
      
      <div class="actions">
        <el-button type="primary" @click="setMockAuth" size="large">
          Set Mock Authentication
        </el-button>
        
        <el-button type="success" @click="goToDashboard" size="large">
          Go to Dashboard
        </el-button>
        
        <el-button type="warning" @click="clearAuth" size="large">
          Clear Authentication
        </el-button>
      </div>
      
      <div class="status">
        <h3>Current Status:</h3>
        <p><strong>Token:</strong> {{ currentToken || 'None' }}</p>
        <p><strong>User:</strong> {{ currentUser?.name || 'None' }}</p>
        <p><strong>Authenticated:</strong> {{ isAuthenticated ? 'Yes' : 'No' }}</p>
      </div>
      
      <div class="instructions">
        <h3>Instructions:</h3>
        <ol>
          <li>Click "Set Mock Authentication" để tạo token giả</li>
          <li>Click "Go to Dashboard" để vào hệ thống</li>
          <li>Hoặc truy cập trực tiếp: <a href="/dashboard">/dashboard</a></li>
        </ol>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// Reactive data
const currentToken = ref('')
const currentUser = ref(null)

// Computed
const isAuthenticated = computed(() => {
  return !!currentToken.value
})

// Methods
const setMockAuth = () => {
  const mockToken = 'mock-jwt-token-' + Date.now()
  const mockUser = {
    id: 1,
    username: 'admin',
    name: 'Administrator',
    email: '<EMAIL>',
    role: 'admin',
    permissions: ['all']
  }

  localStorage.setItem('auth_token', mockToken)
  localStorage.setItem('user_info', JSON.stringify(mockUser))
  
  currentToken.value = mockToken
  currentUser.value = mockUser
  
  ElMessage.success('Mock authentication set successfully!')
}

const goToDashboard = () => {
  if (!isAuthenticated.value) {
    ElMessage.warning('Please set authentication first')
    return
  }
  
  router.push('/dashboard')
}

const clearAuth = () => {
  localStorage.removeItem('auth_token')
  localStorage.removeItem('user_info')
  
  currentToken.value = ''
  currentUser.value = null
  
  ElMessage.info('Authentication cleared')
}

const loadCurrentAuth = () => {
  currentToken.value = localStorage.getItem('auth_token') || ''
  const userInfo = localStorage.getItem('user_info')
  currentUser.value = userInfo ? JSON.parse(userInfo) : null
}

// Lifecycle
onMounted(() => {
  loadCurrentAuth()
})
</script>

<style scoped>
.test-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  padding: 20px;
}

.test-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 40px;
  max-width: 600px;
  width: 100%;
}

.test-card h2 {
  margin: 0 0 16px 0;
  color: #303133;
  text-align: center;
}

.test-card p {
  margin: 0 0 24px 0;
  color: #606266;
  text-align: center;
}

.actions {
  display: flex;
  gap: 12px;
  margin-bottom: 32px;
  flex-wrap: wrap;
}

.actions .el-button {
  flex: 1;
  min-width: 150px;
}

.status {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 24px;
}

.status h3 {
  margin: 0 0 12px 0;
  color: #303133;
}

.status p {
  margin: 8px 0;
  color: #606266;
  text-align: left;
}

.instructions {
  background: #e7f3ff;
  padding: 20px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.instructions h3 {
  margin: 0 0 12px 0;
  color: #303133;
}

.instructions ol {
  margin: 0;
  padding-left: 20px;
  color: #606266;
}

.instructions li {
  margin: 8px 0;
}

.instructions a {
  color: #409eff;
  text-decoration: none;
}

.instructions a:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .actions {
    flex-direction: column;
  }
  
  .actions .el-button {
    width: 100%;
  }
}
</style>
