<template>
  <div class="material-movements">
    <!-- Header -->
    <div class="page-header">
      <div class="header-left">
        <h2>Nh<PERSON><PERSON> k<PERSON>p xuất <PERSON>uyên vật li<PERSON></h2>
        <p><PERSON> dõ<PERSON> lịch sử nhập xuất của các nguyên vật li<PERSON></p>
      </div>
    </div>

    <!-- Bộ lọc -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-input
            v-model="searchQuery"
            placeholder="Tìm kiếm nguyên vật liệu..."
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-select
            v-model="movementTypeFilter"
            placeholder="Loại giao dịch"
            clearable
            @change="handleSearch"
            style="width: 100%"
          >
            <el-option label="Tất cả" value="" />
            <el-option label="Nhập kho" value="IN" />
            <el-option label="Xuất kho" value="OUT" />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="đến"
            start-placeholder="Từ ngày"
            end-placeholder="Đến ngày"
            format="DD/MM/YYYY"
            value-format="YYYY-MM-DD"
            @change="handleSearch"
            style="width: 100%"
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-button @click="resetFilters" :icon="Refresh">
            Đặt lại bộ lọc
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- Bảng nhật ký -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="movements"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column
          prop="movement_id"
          label="ID"
          width="80"
          sortable="custom"
        />
        <el-table-column
          prop="material_name"
          label="Tên nguyên vật liệu"
          min-width="200"
          sortable="custom"
        />
        <el-table-column
          prop="movement_type"
          label="Loại giao dịch"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">
            <el-tag :type="row.movement_type === 'IN' ? 'success' : 'warning'">
              {{ row.movement_type === 'IN' ? 'Nhập kho' : 'Xuất kho' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="quantity"
          label="Số lượng"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">
            <span :class="row.movement_type === 'IN' ? 'quantity-in' : 'quantity-out'">
              {{ row.movement_type === 'IN' ? '+' : '-' }}{{ row.quantity }} {{ row.unit }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="movement_date"
          label="Ngày giao dịch"
          width="150"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.movement_date) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          label="Ghi chú"
          min-width="200"
        >
          <template #default="{ row }">
            {{ row.remark || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="created_by"
          label="Người thực hiện"
          width="120"
        >
          <template #default="{ row }">
            {{ row.created_by || 'System' }}
          </template>
        </el-table-column>
      </el-table>

      <!-- Phân trang -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- Thống kê tổng quan -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <h3>Thống kê theo loại giao dịch</h3>
          </template>
          <div class="stats-content">
            <div class="stat-item">
              <span class="stat-label">Tổng số lần nhập kho:</span>
              <span class="stat-value success">{{ inboundCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Tổng số lần xuất kho:</span>
              <span class="stat-value warning">{{ outboundCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Tổng số giao dịch:</span>
              <span class="stat-value">{{ totalMovements }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <h3>Hoạt động gần đây</h3>
          </template>
          <div class="recent-activities">
            <div 
              v-for="activity in recentMovements" 
              :key="activity.movement_id"
              class="activity-item"
            >
              <div class="activity-icon" :class="activity.movement_type.toLowerCase()">
                <el-icon v-if="activity.movement_type === 'IN'"><Plus /></el-icon>
                <el-icon v-else><Minus /></el-icon>
              </div>
              <div class="activity-content">
                <p class="activity-text">
                  {{ activity.movement_type === 'IN' ? 'Nhập' : 'Xuất' }} 
                  {{ activity.quantity }} {{ activity.unit }} 
                  {{ activity.material_name }}
                </p>
                <span class="activity-time">{{ formatDateTime(activity.movement_date) }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Search, 
  Refresh,
  Plus,
  Minus
} from '@element-plus/icons-vue'
import { materialService } from '@/services'

// Reactive data
const loading = ref(false)
const movements = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const searchQuery = ref('')
const movementTypeFilter = ref('')
const dateRange = ref([])

// Sorting
const sortField = ref('movement_date')
const sortOrder = ref('desc')

// Computed
const totalMovements = computed(() => movements.value.length)
const inboundCount = computed(() => {
  return movements.value.filter(m => m.movement_type === 'IN').length
})
const outboundCount = computed(() => {
  return movements.value.filter(m => m.movement_type === 'OUT').length
})
const recentMovements = computed(() => {
  return movements.value.slice(0, 5)
})

// Methods
const loadMovements = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      movement_type: movementTypeFilter.value,
      start_date: dateRange.value?.[0],
      end_date: dateRange.value?.[1],
      sort_field: sortField.value,
      sort_order: sortOrder.value
    }
    
    const response = await materialService.getMaterialMovements(params)
    
    if (response.success) {
      movements.value = response.data.movements || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('Lỗi khi tải nhật ký:', error)
    ElMessage.error('Không thể tải nhật ký nhập xuất')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadMovements()
}

const resetFilters = () => {
  searchQuery.value = ''
  movementTypeFilter.value = ''
  dateRange.value = []
  sortField.value = 'movement_date'
  sortOrder.value = 'desc'
  currentPage.value = 1
  loadMovements()
}

const handleSortChange = ({ prop, order }) => {
  sortField.value = prop
  sortOrder.value = order === 'ascending' ? 'asc' : 'desc'
  loadMovements()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadMovements()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadMovements()
}

const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('vi-VN')
}

// Lifecycle
onMounted(() => {
  loadMovements()
})
</script>

<style scoped>
.material-movements {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 16px;
}

.table-card {
  margin-bottom: 24px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.quantity-in {
  color: #67c23a;
  font-weight: 600;
}

.quantity-out {
  color: #e6a23c;
  font-weight: 600;
}

.stats-row {
  margin-bottom: 24px;
}

.stats-content {
  padding: 16px 0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}

.stat-value {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.stat-value.success {
  color: #67c23a;
}

.stat-value.warning {
  color: #e6a23c;
}

.recent-activities {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #fff;
}

.activity-icon.in {
  background: #67c23a;
}

.activity-icon.out {
  background: #e6a23c;
}

.activity-content {
  flex: 1;
}

.activity-text {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #303133;
}

.activity-time {
  font-size: 12px;
  color: #909399;
}

/* Responsive */
@media (max-width: 768px) {
  .material-movements {
    padding: 16px;
  }
}
</style>
