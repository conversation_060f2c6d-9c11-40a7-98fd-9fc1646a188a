<template>
  <div class="material-inventory">
    <!-- Header -->
    <div class="page-header">
      <div class="header-left">
        <h2>T<PERSON><PERSON> kho Nguyên vật liệu</h2>
        <p><PERSON> dõi số lượng tồn kho của các nguyên vật liệu</p>
      </div>
      <div class="header-right">
        <el-button 
          type="primary" 
          @click="showInboundDialog = true"
          :icon="Plus"
        >
          Nhập kho
        </el-button>
        <el-button 
          type="warning" 
          @click="showOutboundDialog = true"
          :icon="Minus"
        >
          Xu<PERSON>t kho
        </el-button>
      </div>
    </div>

    <!-- Thống kê tổng quan -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="8">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><Box /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ totalItems }}</h3>
              <p>Tổng số loại nguyên liệu</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="8">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon value">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ formatCurrency(totalValue) }}</h3>
              <p>Tổng giá trị tồn kho</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="8">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon warning">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <h3>{{ lowStockCount }}</h3>
              <p>Nguyên liệu sắp hết</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Bộ lọc -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <el-input
            v-model="searchQuery"
            placeholder="Tìm kiếm nguyên vật liệu..."
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <el-select
            v-model="stockFilter"
            placeholder="Lọc theo trạng thái tồn kho"
            clearable
            @change="handleSearch"
            style="width: 100%"
          >
            <el-option label="Tất cả" value="" />
            <el-option label="Còn hàng" value="in_stock" />
            <el-option label="Sắp hết" value="low_stock" />
            <el-option label="Hết hàng" value="out_of_stock" />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <el-button @click="resetFilters" :icon="Refresh">
            Đặt lại bộ lọc
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- Bảng tồn kho -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="inventory"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column
          prop="material_name"
          label="Tên nguyên vật liệu"
          min-width="200"
          sortable="custom"
        />
        <el-table-column
          prop="supplier"
          label="Nhà cung cấp"
          min-width="150"
        />
        <el-table-column
          prop="current_qty"
          label="Số lượng hiện tại"
          width="150"
          sortable="custom"
        >
          <template #default="{ row }">
            <span :class="getStockStatusClass(row.current_qty)">
              {{ row.current_qty }} {{ row.unit }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="received_qty"
          label="Tổng nhập"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ row.received_qty }} {{ row.unit }}
          </template>
        </el-table-column>
        <el-table-column
          prop="unit_cost"
          label="Giá đơn vị"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ formatCurrency(row.unit_cost) }}
          </template>
        </el-table-column>
        <el-table-column
          label="Giá trị tồn kho"
          width="150"
        >
          <template #default="{ row }">
            {{ formatCurrency(row.current_qty * row.unit_cost) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="in_date"
          label="Ngày nhập gần nhất"
          width="150"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ formatDate(row.in_date) }}
          </template>
        </el-table-column>
        <el-table-column
          label="Trạng thái"
          width="120"
        >
          <template #default="{ row }">
            <el-tag :type="getStockStatusType(row.current_qty)">
              {{ getStockStatusText(row.current_qty) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="Thao tác"
          width="150"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewHistory(row)"
              :icon="View"
            >
              Lịch sử
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- Phân trang -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- Dialog nhập kho -->
    <MaterialInboundDialog
      v-model:visible="showInboundDialog"
      @success="handleMovementSuccess"
    />

    <!-- Dialog xuất kho -->
    <MaterialOutboundDialog
      v-model:visible="showOutboundDialog"
      @success="handleMovementSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Plus, 
  Minus,
  Search, 
  Refresh, 
  View,
  Box,
  Money,
  Warning
} from '@element-plus/icons-vue'
import { materialService } from '@/services'
import MaterialInboundDialog from '@/components/materials/MaterialInboundDialog.vue'
import MaterialOutboundDialog from '@/components/materials/MaterialOutboundDialog.vue'

// Reactive data
const loading = ref(false)
const inventory = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const searchQuery = ref('')
const stockFilter = ref('')
const showInboundDialog = ref(false)
const showOutboundDialog = ref(false)

// Sorting
const sortField = ref('')
const sortOrder = ref('')

// Computed
const totalItems = computed(() => inventory.value.length)
const totalValue = computed(() => {
  return inventory.value.reduce((sum, item) => {
    return sum + (item.current_qty * item.unit_cost)
  }, 0)
})
const lowStockCount = computed(() => {
  return inventory.value.filter(item => item.current_qty < 10).length
})

// Methods
const loadInventory = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      stock_filter: stockFilter.value,
      sort_field: sortField.value,
      sort_order: sortOrder.value
    }
    
    const response = await materialService.getMaterialInventory(params)
    
    if (response.success) {
      inventory.value = response.data.inventory || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('Lỗi khi tải tồn kho:', error)
    ElMessage.error('Không thể tải dữ liệu tồn kho')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadInventory()
}

const resetFilters = () => {
  searchQuery.value = ''
  stockFilter.value = ''
  sortField.value = ''
  sortOrder.value = ''
  currentPage.value = 1
  loadInventory()
}

const handleSortChange = ({ prop, order }) => {
  sortField.value = prop
  sortOrder.value = order === 'ascending' ? 'asc' : 'desc'
  loadInventory()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadInventory()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadInventory()
}

const viewHistory = (material) => {
  // TODO: Implement view history functionality
  ElMessage.info('Tính năng xem lịch sử đang được phát triển')
}

const handleMovementSuccess = () => {
  showInboundDialog.value = false
  showOutboundDialog.value = false
  loadInventory()
}

const getStockStatusClass = (qty) => {
  if (qty === 0) return 'stock-out'
  if (qty < 10) return 'stock-low'
  return 'stock-normal'
}

const getStockStatusType = (qty) => {
  if (qty === 0) return 'danger'
  if (qty < 10) return 'warning'
  return 'success'
}

const getStockStatusText = (qty) => {
  if (qty === 0) return 'Hết hàng'
  if (qty < 10) return 'Sắp hết'
  return 'Còn hàng'
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount)
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('vi-VN')
}

// Lifecycle
onMounted(() => {
  loadInventory()
})
</script>

<style scoped>
.material-inventory {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.stats-row {
  margin-bottom: 24px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: #fff;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.value {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-icon.warning {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stats-info h3 {
  margin: 0 0 4px 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.stats-info p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.filter-card {
  margin-bottom: 16px;
}

.table-card {
  margin-bottom: 24px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.stock-out {
  color: #f56c6c;
  font-weight: 600;
}

.stock-low {
  color: #e6a23c;
  font-weight: 600;
}

.stock-normal {
  color: #67c23a;
  font-weight: 600;
}

/* Responsive */
@media (max-width: 768px) {
  .material-inventory {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-right {
    width: 100%;
    flex-direction: column;
  }
}
</style>
