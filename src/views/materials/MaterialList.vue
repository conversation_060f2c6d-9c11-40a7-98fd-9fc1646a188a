<template>
  <div class="material-list">
    <!-- Header v<PERSON><PERSON> nút thêm mới -->
    <div class="page-header">
      <div class="header-left">
        <h2>Qu<PERSON>n l<PERSON>ê<PERSON> vật liệ<PERSON></h2>
        <p><PERSON><PERSON> sách tất cả nguyên vật liệu trong hệ thống</p>
      </div>
      <div class="header-right">
        <el-button 
          type="primary" 
          @click="showCreateDialog = true"
          :icon="Plus"
        >
          Thêm nguyên vật liệu
        </el-button>
      </div>
    </div>

    <!-- B<PERSON> lọc và tìm kiếm -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <el-input
            v-model="searchQuery"
            placeholder="Tìm kiếm theo tên nguyên vật liệu..."
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <el-input
            v-model="supplierFilter"
            placeholder="Lọc theo nhà cung cấp..."
            clearable
            @input="handleSearch"
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="8">
          <el-button @click="resetFilters" :icon="Refresh">
            Đặt lại bộ lọc
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- Bảng dữ liệu -->
    <el-card class="table-card">
      <ResponsiveTable
        v-loading="loading"
        :data="materials"
        :fields="tableFields"
        title-field="material_name"
        key-field="material_id"
        @sort-change="handleSortChange"
        @card-action="handleCardAction"
      >
        <!-- Desktop table columns -->
        <el-table-column
          prop="material_id"
          label="ID"
          width="80"
          sortable="custom"
        />
        <el-table-column
          prop="material_name"
          label="Tên nguyên vật liệu"
          min-width="200"
          sortable="custom"
        />
        <el-table-column
          prop="supplier"
          label="Nhà cung cấp"
          min-width="150"
          sortable="custom"
        />
        <el-table-column
          prop="unit_cost"
          label="Giá đơn vị"
          width="120"
          sortable="custom"
        >
          <template #default="{ row }">
            {{ formatCurrency(row.unit_cost) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="unit"
          label="Đơn vị tính"
          width="100"
        />
        <el-table-column
          label="Thao tác"
          width="200"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="editMaterial(row)"
              :icon="Edit"
            >
              Sửa
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteMaterial(row)"
              :icon="Delete"
            >
              Xóa
            </el-button>
          </template>
        </el-table-column>

        <!-- Mobile card content -->
        <template #mobile-unit_cost="{ value }">
          <el-tag type="success">{{ formatCurrency(value) }}</el-tag>
        </template>
      </ResponsiveTable>

      <!-- Phân trang -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- Dialog thêm/sửa nguyên vật liệu -->
    <MaterialFormDialog
      v-model:visible="showCreateDialog"
      :material="selectedMaterial"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Refresh,
  Edit,
  Delete
} from '@element-plus/icons-vue'
import { materialService } from '@/services'
import MaterialFormDialog from '@/components/materials/MaterialFormDialog.vue'
import ResponsiveTable from '@/components/common/ResponsiveTable.vue'

// Reactive data
const loading = ref(false)
const materials = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const searchQuery = ref('')
const supplierFilter = ref('')
const showCreateDialog = ref(false)
const selectedMaterial = ref(null)

// Sorting
const sortField = ref('')
const sortOrder = ref('')

// Table configuration for mobile
const tableFields = [
  { key: 'material_id', label: 'ID', type: 'text', hideOnMobile: true },
  { key: 'supplier', label: 'Nhà cung cấp', type: 'text' },
  { key: 'unit_cost', label: 'Giá đơn vị', type: 'currency' },
  { key: 'unit', label: 'Đơn vị', type: 'text' }
]

// Methods
const loadMaterials = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      supplier: supplierFilter.value,
      sort_field: sortField.value,
      sort_order: sortOrder.value
    }
    
    const response = await materialService.getMaterials(params)
    
    if (response.success) {
      materials.value = response.data.materials || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách nguyên vật liệu:', error)
    ElMessage.error('Không thể tải danh sách nguyên vật liệu')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadMaterials()
}

const resetFilters = () => {
  searchQuery.value = ''
  supplierFilter.value = ''
  sortField.value = ''
  sortOrder.value = ''
  currentPage.value = 1
  loadMaterials()
}

const handleSortChange = ({ prop, order }) => {
  sortField.value = prop
  sortOrder.value = order === 'ascending' ? 'asc' : 'desc'
  loadMaterials()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadMaterials()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadMaterials()
}

const editMaterial = (material) => {
  selectedMaterial.value = { ...material }
  showCreateDialog.value = true
}

const deleteMaterial = async (material) => {
  try {
    await ElMessageBox.confirm(
      `Bạn có chắc chắn muốn xóa nguyên vật liệu "${material.material_name}"?`,
      'Xác nhận xóa',
      {
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy',
        type: 'warning',
      }
    )
    
    const response = await materialService.deleteMaterial(material.material_id)
    
    if (response.success) {
      ElMessage.success('Xóa nguyên vật liệu thành công')
      loadMaterials()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Lỗi khi xóa nguyên vật liệu:', error)
      ElMessage.error('Không thể xóa nguyên vật liệu')
    }
  }
}

const handleCardAction = ({ command, item }) => {
  switch (command) {
    case 'edit':
      editMaterial(item)
      break
    case 'delete':
      deleteMaterial(item)
      break
  }
}

const handleFormSuccess = () => {
  showCreateDialog.value = false
  selectedMaterial.value = null
  loadMaterials()
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount)
}

// Lifecycle
onMounted(() => {
  loadMaterials()
})
</script>

<style scoped>
.material-list {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 16px;
}

.table-card {
  margin-bottom: 24px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* Responsive */
@media (max-width: 768px) {
  .material-list {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-right {
    width: 100%;
  }
  
  .header-right .el-button {
    width: 100%;
  }
}
</style>
