import { createRouter, createWebHistory } from "vue-router";
import { setupGuards } from "./guards";

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: "/login",
      name: "Login",
      component: () => import("@/views/Login.vue"),
      meta: {
        title: "Đăng nhập",
        requiresAuth: false
      }
    },
    {
      path: "/",
      name: "MainLayout",
      component: () => import("@/views/MainPage.vue"),
      redirect: "/dashboard",
      meta: { requiresAuth: true },
      children: [
        {
          path: "dashboard",
          name: "Dashboard",
          component: () => import("@/views/Dashboard.vue"),
          meta: {
            title: "Dashboard",
            requiresAuth: true,
            icon: "House"
          }
        },
        // Nguyên vật liệu routes
        {
          path: "materials",
          name: "Materials",
          component: () => import("@/views/materials/MaterialList.vue"),
          meta: {
            title: "Quản lý Nguyên vật liệu",
            requiresAuth: true,
            icon: "Box"
          }
        },
        {
          path: "materials/inventory",
          name: "MaterialInventory",
          component: () => import("@/views/materials/MaterialInventory.vue"),
          meta: {
            title: "Tồn kho Nguyên vật liệu",
            requiresAuth: true,
            icon: "Goods"
          }
        },
        {
          path: "materials/movements",
          name: "MaterialMovements",
          component: () => import("@/views/materials/MaterialMovements.vue"),
          meta: {
            title: "Nhật ký Nguyên vật liệu",
            requiresAuth: true,
            icon: "TrendCharts"
          }
        },
        // Sản phẩm routes
        {
          path: "products",
          name: "Products",
          component: () => import("@/views/products/ProductList.vue"),
        },
        {
          path: "products/inventory",
          name: "ProductInventory",
          component: () => import("@/views/products/ProductInventory.vue"),
        },
        {
          path: "products/movements",
          name: "ProductMovements",
          component: () => import("@/views/products/ProductMovements.vue"),
        },
        // BOM routes
        {
          path: "bom",
          name: "BOM",
          component: () => import("@/views/bom/BOMList.vue"),
        },
        // Đơn hàng routes
        {
          path: "orders",
          name: "Orders",
          component: () => import("@/views/orders/OrderList.vue"),
        },
        {
          path: "orders/create",
          name: "CreateOrder",
          component: () => import("@/views/orders/CreateOrder.vue"),
        },
        {
          path: "orders/:id",
          name: "OrderDetail",
          component: () => import("@/views/orders/OrderDetail.vue"),
        },
        // Báo cáo routes
        {
          path: "reports/inventory",
          name: "InventoryReport",
          component: () => import("@/views/reports/InventoryReport.vue"),
        },
        {
          path: "reports/production",
          name: "ProductionReport",
          component: () => import("@/views/reports/ProductionReport.vue"),
        },
        {
          path: "reports/orders",
          name: "OrderReport",
          component: () => import("@/views/reports/OrderReport.vue"),
        },
        // Cài đặt routes
        {
          path: "settings",
          name: "Settings",
          component: () => import("@/views/Settings.vue"),
        },
      ],
    },
  ],
});

// Setup route guards
setupGuards(router);

export default router;
