/**
 * Route guards cho authentication và authorization
 */

import { authService } from '@/services'
import { ElMessage } from 'element-plus'

/**
 * Guard kiểm tra authentication
 * @param {Object} to - Route đích
 * @param {Object} from - Route hiện tại
 * @param {Function} next - Callback next
 */
export const authGuard = (to, from, next) => {
  const isAuthenticated = authService.isAuthenticated()
  
  // Nếu route yêu cầu authentication
  if (to.meta.requiresAuth) {
    if (!isAuthenticated) {
      ElMessage.warning('Vui lòng đăng nhập để tiếp tục')
      next('/login')
      return
    }
  }
  
  // Nếu đã đăng nhập và truy cập trang login
  if (to.path === '/login' && isAuthenticated) {
    next('/dashboard')
    return
  }
  
  next()
}

/**
 * Guard kiểm tra quyền truy cập
 * @param {Object} to - Route đích
 * @param {Object} from - Route hiện tại
 * @param {Function} next - Callback next
 */
export const permissionGuard = (to, from, next) => {
  const user = authService.getCurrentUser()
  
  // Nếu route yêu cầu quyền đặc biệt
  if (to.meta.requiredPermissions) {
    if (!user || !user.permissions) {
      ElMessage.error('Bạn không có quyền truy cập trang này')
      next('/dashboard')
      return
    }
    
    const hasPermission = to.meta.requiredPermissions.some(permission => 
      user.permissions.includes(permission)
    )
    
    if (!hasPermission) {
      ElMessage.error('Bạn không có quyền truy cập trang này')
      next('/dashboard')
      return
    }
  }
  
  next()
}

/**
 * Guard kiểm tra role
 * @param {Object} to - Route đích
 * @param {Object} from - Route hiện tại
 * @param {Function} next - Callback next
 */
export const roleGuard = (to, from, next) => {
  const user = authService.getCurrentUser()
  
  // Nếu route yêu cầu role đặc biệt
  if (to.meta.requiredRoles) {
    if (!user || !user.role) {
      ElMessage.error('Bạn không có quyền truy cập trang này')
      next('/dashboard')
      return
    }
    
    const hasRole = to.meta.requiredRoles.includes(user.role)
    
    if (!hasRole) {
      ElMessage.error('Bạn không có quyền truy cập trang này')
      next('/dashboard')
      return
    }
  }
  
  next()
}

/**
 * Guard kiểm tra token hết hạn
 * @param {Object} to - Route đích
 * @param {Object} from - Route hiện tại
 * @param {Function} next - Callback next
 */
export const tokenGuard = async (to, from, next) => {
  const token = authService.getToken()
  
  if (token) {
    try {
      // Kiểm tra token có hết hạn không
      const payload = JSON.parse(atob(token.split('.')[1]))
      const currentTime = Date.now() / 1000
      
      if (payload.exp < currentTime) {
        // Token đã hết hạn, thử refresh
        try {
          await authService.refreshToken()
        } catch (error) {
          // Refresh thất bại, đăng xuất
          authService.logout()
          ElMessage.warning('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.')
          next('/login')
          return
        }
      }
    } catch (error) {
      // Token không hợp lệ
      authService.logout()
      ElMessage.error('Token không hợp lệ. Vui lòng đăng nhập lại.')
      next('/login')
      return
    }
  }
  
  next()
}

/**
 * Guard ghi log truy cập
 * @param {Object} to - Route đích
 * @param {Object} from - Route hiện tại
 * @param {Function} next - Callback next
 */
export const loggingGuard = (to, from, next) => {
  const user = authService.getCurrentUser()
  
  // Ghi log truy cập (chỉ trong môi trường development)
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Route Access] ${user?.username || 'Anonymous'} -> ${to.path}`)
  }
  
  // TODO: Gửi log lên server nếu cần
  
  next()
}

/**
 * Cấu hình tất cả guards
 */
export const setupGuards = (router) => {
  // Thứ tự guards quan trọng
  router.beforeEach(tokenGuard)
  router.beforeEach(authGuard)
  router.beforeEach(permissionGuard)
  router.beforeEach(roleGuard)
  router.beforeEach(loggingGuard)
  
  // After each guard
  router.afterEach((to, from) => {
    // Cập nhật title trang
    if (to.meta.title) {
      document.title = `${to.meta.title} - ERP System`
    } else {
      document.title = 'ERP System - Quản lý Sản xuất'
    }
    
    // Scroll to top
    window.scrollTo(0, 0)
  })
}

export default {
  authGuard,
  permissionGuard,
  roleGuard,
  tokenGuard,
  loggingGuard,
  setupGuards
}
