<template>
  <div class="inventory-value-chart">
    <div class="chart-header">
      <h3><PERSON>ân bố giá trị tồn kho</h3>
      <div class="chart-controls">
        <el-switch
          v-model="showPercentage"
          active-text="Phần trăm"
          inactive-text="Giá trị"
          size="small"
          @change="updateChart"
        />
      </div>
    </div>
    
    <BaseChart
      :option="chartOption"
      :loading="loading"
      height="350px"
    />
    
    <!-- Summary Stats -->
    <div class="chart-summary">
      <div class="summary-item">
        <span class="summary-label">Tổng giá trị:</span>
        <span class="summary-value">{{ formatCurrency(totalValue) }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">Loại hàng hóa:</span>
        <span class="summary-value">{{ chartData.length }} loại</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import BaseChart from '@/components/common/BaseChart.vue'

// Props
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Reactive data
const showPercentage = ref(true)

// Mock data for demonstration
const mockData = [
  { name: 'Nhựa PVC', value: 1500000000, category: 'material' },
  { name: 'Nhựa ABS', value: 800000000, category: 'material' },
  { name: 'Màu đỏ', value: 200000000, category: 'material' },
  { name: 'Màu xanh', value: 150000000, category: 'material' },
  { name: 'Ống nhựa 20mm', value: 600000000, category: 'product' },
  { name: 'Ống nhựa 25mm', value: 450000000, category: 'product' },
  { name: 'Phụ kiện', value: 300000000, category: 'product' }
]

// Computed
const chartData = computed(() => {
  return props.data.length > 0 ? props.data : mockData
})

const totalValue = computed(() => {
  return chartData.value.reduce((sum, item) => sum + item.value, 0)
})

const chartOption = computed(() => {
  const colors = [
    '#e7ab3c', '#67c23a', '#409eff', '#f56c6c', '#e6a23c',
    '#9c27b0', '#ff9800', '#e91e63', '#795548', '#9e9e9e'
  ]
  
  return {
    title: {
      text: showPercentage.value ? 'Phân bố theo phần trăm' : 'Phân bố theo giá trị',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      },
      left: 'center',
      top: 10
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const value = showPercentage.value 
          ? `${params.percent}%`
          : formatCurrency(params.value)
        return `
          <div style="font-weight: 600;">${params.name}</div>
          <div style="margin: 4px 0;">
            <span style="display: inline-block; width: 10px; height: 10px; background: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
            Giá trị: <strong>${value}</strong>
          </div>
        `
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      textStyle: {
        fontSize: 12
      },
      formatter: function(name) {
        const item = chartData.value.find(d => d.name === name)
        if (item) {
          const value = showPercentage.value 
            ? `${((item.value / totalValue.value) * 100).toFixed(1)}%`
            : formatCurrency(item.value)
          return `${name}: ${value}`
        }
        return name
      }
    },
    series: [
      {
        name: 'Giá trị tồn kho',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['65%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold',
            formatter: function(params) {
              const value = showPercentage.value 
                ? `${params.percent}%`
                : formatCurrency(params.value)
              return `${params.name}\n${value}`
            }
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: false
        },
        data: chartData.value.map((item, index) => ({
          ...item,
          itemStyle: {
            color: colors[index % colors.length]
          }
        }))
      }
    ]
  }
})

// Methods
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

const updateChart = () => {
  // Chart will automatically update due to computed property
}
</script>

<style scoped>
.inventory-value-chart {
  width: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 16px;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-summary {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-top: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.summary-label {
  font-size: 12px;
  color: #909399;
}

.summary-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* Mobile styles */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 0 8px;
  }
  
  .chart-controls {
    width: 100%;
    justify-content: flex-end;
  }
  
  .chart-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .summary-item {
    flex-direction: row;
    justify-content: space-between;
  }
}
</style>
