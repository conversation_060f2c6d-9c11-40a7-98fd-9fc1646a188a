<template>
  <div class="inventory-trend-chart">
    <div class="chart-header">
      <h3><PERSON> tồn kho</h3>
      <div class="chart-controls">
        <el-select
          v-model="selectedPeriod"
          size="small"
          style="width: 120px"
          @change="updateChart"
        >
          <el-option label="7 ngày" value="7d" />
          <el-option label="30 ngày" value="30d" />
          <el-option label="90 ngày" value="90d" />
        </el-select>
      </div>
    </div>
    
    <BaseChart
      :option="chartOption"
      :loading="loading"
      height="350px"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import BaseChart from '@/components/common/BaseChart.vue'

// Props
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Reactive data
const selectedPeriod = ref('30d')

// Mock data for demonstration
const mockData = {
  '7d': {
    dates: ['2025-06-11', '2025-06-12', '2025-06-13', '2025-06-14', '2025-06-15', '2025-06-16', '2025-06-17'],
    materials: [120, 118, 125, 122, 130, 128, 135],
    products: [85, 88, 82, 90, 87, 92, 89]
  },
  '30d': {
    dates: Array.from({ length: 30 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - (29 - i))
      return date.toISOString().split('T')[0]
    }),
    materials: Array.from({ length: 30 }, () => Math.floor(Math.random() * 50) + 100),
    products: Array.from({ length: 30 }, () => Math.floor(Math.random() * 30) + 70)
  },
  '90d': {
    dates: Array.from({ length: 90 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - (89 - i))
      return date.toISOString().split('T')[0]
    }),
    materials: Array.from({ length: 90 }, () => Math.floor(Math.random() * 80) + 80),
    products: Array.from({ length: 90 }, () => Math.floor(Math.random() * 40) + 60)
  }
}

// Computed
const chartOption = computed(() => {
  const data = props.data.length > 0 ? props.data : mockData[selectedPeriod.value]
  
  return {
    title: {
      text: 'Biến động tồn kho theo thời gian',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      },
      left: 'center',
      top: 10
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: function(params) {
        let result = `<div style="font-weight: 600;">${params[0].axisValue}</div>`
        params.forEach(param => {
          result += `
            <div style="margin: 4px 0;">
              <span style="display: inline-block; width: 10px; height: 10px; background: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              ${param.seriesName}: <strong>${param.value}</strong>
            </div>
          `
        })
        return result
      }
    },
    legend: {
      data: ['Nguyên vật liệu', 'Sản phẩm'],
      top: 35,
      left: 'center'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '20%',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {
          title: 'Lưu ảnh'
        },
        dataZoom: {
          title: {
            zoom: 'Phóng to',
            back: 'Khôi phục'
          }
        }
      },
      right: 20,
      top: 10
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.dates,
      axisLabel: {
        formatter: function(value) {
          const date = new Date(value)
          return `${date.getDate()}/${date.getMonth() + 1}`
        }
      }
    },
    yAxis: {
      type: 'value',
      name: 'Số lượng',
      nameLocation: 'middle',
      nameGap: 40,
      axisLabel: {
        formatter: '{value}'
      }
    },
    series: [
      {
        name: 'Nguyên vật liệu',
        type: 'line',
        stack: false,
        smooth: true,
        lineStyle: {
          width: 3
        },
        areaStyle: {
          opacity: 0.3
        },
        data: data.materials,
        itemStyle: {
          color: '#e7ab3c'
        }
      },
      {
        name: 'Sản phẩm',
        type: 'line',
        stack: false,
        smooth: true,
        lineStyle: {
          width: 3
        },
        areaStyle: {
          opacity: 0.3
        },
        data: data.products,
        itemStyle: {
          color: '#67c23a'
        }
      }
    ],
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100,
        height: 20,
        bottom: 10
      }
    ]
  }
})

// Methods
const updateChart = () => {
  // Chart will automatically update due to computed property
}

// Lifecycle
onMounted(() => {
  updateChart()
})
</script>

<style scoped>
.inventory-trend-chart {
  width: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 16px;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Mobile styles */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 0 8px;
  }
  
  .chart-controls {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
