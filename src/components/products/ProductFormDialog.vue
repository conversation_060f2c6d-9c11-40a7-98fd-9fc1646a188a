<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? 'Chỉnh sửa sản phẩm' : 'Thêm sản phẩm mới'"
    width="700px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="160px"
      label-position="left"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="Tên sản phẩm" prop="product_name">
            <el-input
              v-model="formData.product_name"
              placeholder="Nhập tên sản phẩm"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="Trọng lượng (gram)" prop="product_weight_g">
            <el-input-number
              v-model="formData.product_weight_g"
              :min="0"
              :precision="2"
              :step="0.1"
              placeholder="Nhập trọng lượng"
              style="width: 100%"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Thời gian ép (giây)" prop="injection_time_s">
            <el-input-number
              v-model="formData.injection_time_s"
              :min="0"
              :precision="0"
              :step="1"
              placeholder="Nhập thời gian ép"
              style="width: 100%"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="Màu sắc" prop="color">
            <el-select
              v-model="formData.color"
              placeholder="Chọn màu sắc"
              style="width: 100%"
              filterable
              allow-create
            >
              <el-option
                v-for="color in commonColors"
                :key="color.value"
                :label="color.label"
                :value="color.value"
              >
                <div style="display: flex; align-items: center;">
                  <div 
                    :style="{ 
                      width: '16px', 
                      height: '16px', 
                      backgroundColor: color.code,
                      border: '1px solid #ddd',
                      borderRadius: '2px',
                      marginRight: '8px'
                    }"
                  ></div>
                  {{ color.label }}
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="Giá đơn vị (VNĐ)" prop="unit_price">
            <el-input-number
              v-model="formData.unit_price"
              :min="0"
              :precision="0"
              :step="1000"
              placeholder="Nhập giá đơn vị"
              style="width: 100%"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="Mô tả" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="3"
              placeholder="Nhập mô tả sản phẩm (tùy chọn)"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">Hủy</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
        >
          {{ isEdit ? 'Cập nhật' : 'Thêm mới' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { productService } from '@/services'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  product: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Refs
const formRef = ref()

// Reactive data
const submitting = ref(false)
const dialogVisible = ref(false)

const formData = reactive({
  product_name: '',
  product_weight_g: 0,
  injection_time_s: 0,
  color: '',
  unit_price: 0,
  description: ''
})

// Common colors for selection
const commonColors = [
  { label: 'Đỏ', value: 'Đỏ', code: '#f56c6c' },
  { label: 'Xanh dương', value: 'Xanh', code: '#409eff' },
  { label: 'Vàng', value: 'Vàng', code: '#e6a23c' },
  { label: 'Xanh lá', value: 'Xanh lá', code: '#67c23a' },
  { label: 'Tím', value: 'Tím', code: '#9c27b0' },
  { label: 'Cam', value: 'Cam', code: '#ff9800' },
  { label: 'Hồng', value: 'Hồng', code: '#e91e63' },
  { label: 'Nâu', value: 'Nâu', code: '#795548' },
  { label: 'Xám', value: 'Xám', code: '#9e9e9e' },
  { label: 'Đen', value: 'Đen', code: '#424242' },
  { label: 'Trắng', value: 'Trắng', code: '#ffffff' }
]

// Form validation rules
const formRules = {
  product_name: [
    { required: true, message: 'Vui lòng nhập tên sản phẩm', trigger: 'blur' },
    { min: 2, max: 100, message: 'Tên sản phẩm phải từ 2-100 ký tự', trigger: 'blur' }
  ],
  product_weight_g: [
    { required: true, message: 'Vui lòng nhập trọng lượng', trigger: 'blur' },
    { type: 'number', min: 0, message: 'Trọng lượng phải lớn hơn hoặc bằng 0', trigger: 'blur' }
  ],
  injection_time_s: [
    { required: true, message: 'Vui lòng nhập thời gian ép', trigger: 'blur' },
    { type: 'number', min: 0, message: 'Thời gian ép phải lớn hơn hoặc bằng 0', trigger: 'blur' }
  ],
  color: [
    { required: true, message: 'Vui lòng chọn màu sắc', trigger: 'change' }
  ],
  unit_price: [
    { required: true, message: 'Vui lòng nhập giá đơn vị', trigger: 'blur' },
    { type: 'number', min: 0, message: 'Giá đơn vị phải lớn hơn hoặc bằng 0', trigger: 'blur' }
  ]
}

// Computed
const isEdit = computed(() => {
  return props.product && props.product.product_id
})

// Watchers
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetForm()
    if (props.product) {
      Object.assign(formData, props.product)
    }
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// Methods
const resetForm = () => {
  Object.assign(formData, {
    product_name: '',
    product_weight_g: 0,
    injection_time_s: 0,
    color: '',
    unit_price: 0,
    description: ''
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  try {
    // Validate form
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    let response
    if (isEdit.value) {
      // Update existing product
      response = await productService.updateProduct(props.product.product_id, formData)
    } else {
      // Create new product
      response = await productService.createProduct(formData)
    }

    if (response.success) {
      ElMessage.success(
        isEdit.value 
          ? 'Cập nhật sản phẩm thành công' 
          : 'Thêm sản phẩm thành công'
      )
      emit('success')
      handleClose()
    }
  } catch (error) {
    console.error('Lỗi khi lưu sản phẩm:', error)
    ElMessage.error(
      isEdit.value 
        ? 'Không thể cập nhật sản phẩm' 
        : 'Không thể thêm sản phẩm'
    )
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}
</style>
