<template>
  <div class="quick-actions" :class="{ 'mobile-actions': isMobile }">
    <!-- Quick Action Button -->
    <el-button
      type="primary"
      :size="isMobile ? 'default' : 'large'"
      class="quick-btn"
      @click="showQuickMenu = !showQuickMenu"
      :icon="Plus"
      circle
      v-if="!showQuickMenu"
    />
    
    <!-- Quick Actions Menu -->
    <transition name="quick-menu">
      <div v-if="showQuickMenu" class="quick-menu">
        <div class="quick-menu-header">
          <h3>Thao tác nhanh</h3>
          <el-button
            type="text"
            @click="showQuickMenu = false"
            class="close-btn"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        
        <div class="quick-menu-content">
          <div class="action-group">
            <h4>Nguyên vật liệu</h4>
            <div class="action-buttons">
              <el-button
                type="primary"
                @click="handleQuickAction('add-material')"
                :icon="Plus"
                size="small"
              >
                Thêm nguyên liệu
              </el-button>
              <el-button
                type="success"
                @click="handleQuickAction('material-inbound')"
                :icon="Download"
                size="small"
              >
                Nhập kho
              </el-button>
              <el-button
                type="warning"
                @click="handleQuickAction('material-outbound')"
                :icon="Upload"
                size="small"
              >
                Xuất kho
              </el-button>
            </div>
          </div>
          
          <div class="action-group">
            <h4>Sản phẩm</h4>
            <div class="action-buttons">
              <el-button
                type="primary"
                @click="handleQuickAction('add-product')"
                :icon="Plus"
                size="small"
              >
                Thêm sản phẩm
              </el-button>
              <el-button
                type="success"
                @click="handleQuickAction('product-inbound')"
                :icon="Download"
                size="small"
              >
                Nhập kho
              </el-button>
            </div>
          </div>
          
          <div class="action-group">
            <h4>Đơn hàng</h4>
            <div class="action-buttons">
              <el-button
                type="primary"
                @click="handleQuickAction('create-order')"
                :icon="Document"
                size="small"
              >
                Tạo đơn hàng
              </el-button>
              <el-button
                type="info"
                @click="handleQuickAction('create-bom')"
                :icon="Connection"
                size="small"
              >
                Tạo BOM
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </transition>
    
    <!-- Overlay -->
    <div
      v-if="showQuickMenu"
      class="quick-overlay"
      @click="showQuickMenu = false"
    ></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Plus,
  Close,
  Download,
  Upload,
  Document,
  Connection
} from '@element-plus/icons-vue'

// Router
const router = useRouter()

// Reactive data
const showQuickMenu = ref(false)
const isMobile = ref(false)

// Emits
const emit = defineEmits(['quick-action'])

// Methods
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768
}

const handleQuickAction = (action) => {
  showQuickMenu.value = false
  
  switch (action) {
    case 'add-material':
      emit('quick-action', { type: 'add-material' })
      break
    case 'material-inbound':
      emit('quick-action', { type: 'material-inbound' })
      break
    case 'material-outbound':
      emit('quick-action', { type: 'material-outbound' })
      break
    case 'add-product':
      emit('quick-action', { type: 'add-product' })
      break
    case 'product-inbound':
      emit('quick-action', { type: 'product-inbound' })
      break
    case 'create-order':
      router.push('/orders/create')
      break
    case 'create-bom':
      router.push('/bom')
      break
    default:
      ElMessage.info(`Thao tác ${action} đang được phát triển`)
  }
}

// Lifecycle
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})
</script>

<style scoped>
.quick-actions {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
}

.mobile-actions {
  bottom: 16px;
  right: 16px;
}

.quick-btn {
  width: 56px;
  height: 56px;
  box-shadow: 0 4px 12px rgba(231, 171, 60, 0.4);
  background: linear-gradient(135deg, #e7ab3c 0%, #d4941a 100%);
  border: none;
  transition: all 0.3s ease;
}

.quick-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(231, 171, 60, 0.6);
}

.quick-menu {
  position: absolute;
  bottom: 70px;
  right: 0;
  width: 320px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  transform-origin: bottom right;
}

.quick-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #e7ab3c 0%, #d4941a 100%);
  color: #fff;
}

.quick-menu-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-btn {
  color: #fff;
  padding: 4px;
  min-width: auto;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.quick-menu-content {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.action-group {
  margin-bottom: 20px;
}

.action-group:last-child {
  margin-bottom: 0;
}

.action-group h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.action-buttons .el-button {
  flex: 1;
  min-width: 120px;
}

.quick-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(2px);
  z-index: -1;
}

/* Animations */
.quick-menu-enter-active,
.quick-menu-leave-active {
  transition: all 0.3s ease;
}

.quick-menu-enter-from {
  opacity: 0;
  transform: scale(0.8) translateY(20px);
}

.quick-menu-leave-to {
  opacity: 0;
  transform: scale(0.8) translateY(20px);
}

/* Mobile styles */
@media (max-width: 768px) {
  .quick-menu {
    width: calc(100vw - 32px);
    right: -8px;
    bottom: 60px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .el-button {
    width: 100%;
    min-width: auto;
  }
  
  .quick-btn {
    width: 48px;
    height: 48px;
  }
}

/* Custom scrollbar */
.quick-menu-content::-webkit-scrollbar {
  width: 4px;
}

.quick-menu-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.quick-menu-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}
</style>
