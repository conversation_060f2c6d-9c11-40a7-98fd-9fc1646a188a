<template>
  <el-dialog
    v-model="dialogVisible"
    :title="wizardTitle"
    :width="isMobile ? '95%' : '800px'"
    :before-close="handleClose"
    class="wizard-dialog"
  >
    <!-- Steps -->
    <el-steps
      :active="currentStep"
      :direction="isMobile ? 'vertical' : 'horizontal'"
      class="wizard-steps"
      finish-status="success"
    >
      <el-step
        v-for="(step, index) in steps"
        :key="index"
        :title="step.title"
        :description="step.description"
        :icon="step.icon"
      />
    </el-steps>

    <!-- Step Content -->
    <div class="wizard-content">
      <!-- Step 1: Chọn loại và mục đích -->
      <div v-if="currentStep === 0" class="step-content">
        <h3>Chọn loại và mục đích</h3>
        <el-form :model="wizardData" label-width="140px">
          <el-form-item label="Loại giao dịch">
            <el-radio-group v-model="wizardData.type" @change="handleTypeChange">
              <el-radio label="material"><PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> liệ<PERSON></el-radio>
              <el-radio label="product"><PERSON><PERSON>n phẩm</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="Hướng giao dịch">
            <el-radio-group v-model="wizardData.direction">
              <el-radio label="IN">
                <el-icon><Download /></el-icon>
                Nhập kho
              </el-radio>
              <el-radio label="OUT">
                <el-icon><Upload /></el-icon>
                Xuất kho
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- Step 2: Chọn mặt hàng -->
      <div v-if="currentStep === 1" class="step-content">
        <h3>Chọn {{ wizardData.type === 'material' ? 'nguyên vật liệu' : 'sản phẩm' }}</h3>
        <el-form :model="wizardData" label-width="140px">
          <el-form-item :label="wizardData.type === 'material' ? 'Nguyên vật liệu' : 'Sản phẩm'">
            <el-select
              v-model="wizardData.itemId"
              placeholder="Chọn mặt hàng"
              style="width: 100%"
              filterable
              remote
              :remote-method="searchItems"
              :loading="itemsLoading"
              @change="handleItemChange"
            >
              <el-option
                v-for="item in items"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
                <div class="item-option">
                  <span class="item-name">{{ item.name }}</span>
                  <span class="item-info">
                    {{ wizardData.type === 'material' ? item.supplier : item.color }}
                    {{ wizardData.direction === 'OUT' ? `(Tồn: ${item.stock || 0})` : '' }}
                  </span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="selectedItem" label="Thông tin">
            <div class="item-details">
              <p><strong>{{ selectedItem.name }}</strong></p>
              <p v-if="wizardData.type === 'material'">
                Nhà cung cấp: {{ selectedItem.supplier }}
              </p>
              <p v-else>
                Màu sắc: {{ selectedItem.color }} | 
                Trọng lượng: {{ selectedItem.weight }}g
              </p>
              <p v-if="wizardData.direction === 'OUT'">
                Tồn kho hiện tại: {{ selectedItem.stock || 0 }} {{ selectedItem.unit }}
              </p>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- Step 3: Nhập số lượng và ghi chú -->
      <div v-if="currentStep === 2" class="step-content">
        <h3>Nhập số lượng và ghi chú</h3>
        <el-form :model="wizardData" :rules="quantityRules" ref="quantityFormRef" label-width="140px">
          <el-form-item label="Số lượng" prop="quantity">
            <el-input-number
              v-model="wizardData.quantity"
              :min="0.1"
              :max="wizardData.direction === 'OUT' ? selectedItem?.stock : undefined"
              :precision="2"
              :step="1"
              style="width: 100%"
              controls-position="right"
            />
            <div class="quantity-info">
              Đơn vị: {{ selectedItem?.unit || 'N/A' }}
              <span v-if="wizardData.direction === 'OUT' && selectedItem">
                (Tối đa: {{ selectedItem.stock }})
              </span>
            </div>
          </el-form-item>
          
          <el-form-item label="Ghi chú">
            <el-input
              v-model="wizardData.remark"
              type="textarea"
              :rows="3"
              placeholder="Nhập ghi chú (tùy chọn)"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- Step 4: Xác nhận -->
      <div v-if="currentStep === 3" class="step-content">
        <h3>Xác nhận thông tin</h3>
        <div class="confirmation-details">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="Loại giao dịch">
              <el-tag :type="wizardData.direction === 'IN' ? 'success' : 'warning'">
                {{ wizardData.direction === 'IN' ? 'Nhập kho' : 'Xuất kho' }}
                {{ wizardData.type === 'material' ? 'nguyên vật liệu' : 'sản phẩm' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item :label="wizardData.type === 'material' ? 'Nguyên vật liệu' : 'Sản phẩm'">
              {{ selectedItem?.name }}
            </el-descriptions-item>
            <el-descriptions-item label="Số lượng">
              <strong>{{ wizardData.quantity }} {{ selectedItem?.unit }}</strong>
            </el-descriptions-item>
            <el-descriptions-item v-if="wizardData.remark" label="Ghi chú">
              {{ wizardData.remark }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <template #footer>
      <div class="wizard-actions">
        <el-button @click="handleClose">Hủy</el-button>
        <el-button
          v-if="currentStep > 0"
          @click="prevStep"
        >
          Quay lại
        </el-button>
        <el-button
          v-if="currentStep < steps.length - 1"
          type="primary"
          @click="nextStep"
          :disabled="!canProceed"
        >
          Tiếp theo
        </el-button>
        <el-button
          v-if="currentStep === steps.length - 1"
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          Xác nhận
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Upload } from '@element-plus/icons-vue'
import { materialService, productService } from '@/services'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  initialType: {
    type: String,
    default: 'material' // material | product
  },
  initialDirection: {
    type: String,
    default: 'IN' // IN | OUT
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Refs
const quantityFormRef = ref()

// Reactive data
const dialogVisible = ref(false)
const currentStep = ref(0)
const submitting = ref(false)
const itemsLoading = ref(false)
const items = ref([])
const isMobile = ref(false)

const wizardData = reactive({
  type: 'material',
  direction: 'IN',
  itemId: null,
  quantity: 1,
  remark: ''
})

// Steps configuration
const steps = [
  {
    title: 'Loại giao dịch',
    description: 'Chọn loại và hướng',
    icon: 'Setting'
  },
  {
    title: 'Chọn mặt hàng',
    description: 'Chọn nguyên liệu/sản phẩm',
    icon: 'Box'
  },
  {
    title: 'Số lượng',
    description: 'Nhập số lượng và ghi chú',
    icon: 'EditPen'
  },
  {
    title: 'Xác nhận',
    description: 'Kiểm tra và xác nhận',
    icon: 'Check'
  }
]

// Validation rules
const quantityRules = {
  quantity: [
    { required: true, message: 'Vui lòng nhập số lượng', trigger: 'blur' },
    { type: 'number', min: 0.1, message: 'Số lượng phải lớn hơn 0', trigger: 'blur' }
  ]
}

// Computed
const wizardTitle = computed(() => {
  const typeText = wizardData.type === 'material' ? 'Nguyên vật liệu' : 'Sản phẩm'
  const directionText = wizardData.direction === 'IN' ? 'Nhập kho' : 'Xuất kho'
  return `${directionText} ${typeText}`
})

const selectedItem = computed(() => {
  return items.value.find(item => item.id === wizardData.itemId)
})

const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0:
      return wizardData.type && wizardData.direction
    case 1:
      return wizardData.itemId
    case 2:
      return wizardData.quantity > 0
    default:
      return true
  }
})

// Methods
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768
}

const handleTypeChange = () => {
  wizardData.itemId = null
  items.value = []
}

const searchItems = async (query) => {
  try {
    itemsLoading.value = true
    const service = wizardData.type === 'material' ? materialService : productService
    const response = await service.getItems({ search: query, limit: 50 })
    
    if (response.success) {
      items.value = response.data.items?.map(item => ({
        id: item.id,
        name: item.name,
        supplier: item.supplier,
        color: item.color,
        weight: item.weight,
        unit: item.unit,
        stock: item.current_qty || 0
      })) || []
    }
  } catch (error) {
    console.error('Error searching items:', error)
  } finally {
    itemsLoading.value = false
  }
}

const handleItemChange = () => {
  wizardData.quantity = 1
}

const nextStep = async () => {
  if (currentStep.value === 2) {
    const valid = await quantityFormRef.value?.validate()
    if (!valid) return
  }
  
  if (currentStep.value < steps.length - 1) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const handleSubmit = async () => {
  try {
    submitting.value = true
    
    const service = wizardData.type === 'material' ? materialService : productService
    const response = await service.recordMovement({
      itemId: wizardData.itemId,
      type: wizardData.direction,
      quantity: wizardData.quantity,
      remark: wizardData.remark
    })
    
    if (response.success) {
      ElMessage.success('Ghi nhận giao dịch thành công!')
      emit('success')
      handleClose()
    }
  } catch (error) {
    console.error('Error submitting:', error)
    ElMessage.error('Không thể ghi nhận giao dịch')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  currentStep.value = 0
  Object.assign(wizardData, {
    type: 'material',
    direction: 'IN',
    itemId: null,
    quantity: 1,
    remark: ''
  })
}

// Watchers
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    wizardData.type = props.initialType
    wizardData.direction = props.initialDirection
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// Lifecycle
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})
</script>

<style scoped>
.wizard-dialog {
  border-radius: 12px;
}

.wizard-steps {
  margin-bottom: 32px;
}

.wizard-content {
  min-height: 300px;
  padding: 0 16px;
}

.step-content h3 {
  margin: 0 0 24px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.item-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-name {
  font-weight: 500;
}

.item-info {
  font-size: 12px;
  color: #909399;
}

.item-details {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border-left: 4px solid #e7ab3c;
}

.item-details p {
  margin: 4px 0;
  color: #606266;
}

.quantity-info {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.confirmation-details {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.wizard-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Mobile styles */
@media (max-width: 768px) {
  .wizard-content {
    padding: 0 8px;
  }
  
  .wizard-actions {
    flex-direction: column-reverse;
  }
  
  .wizard-actions .el-button {
    width: 100%;
  }
}
</style>
