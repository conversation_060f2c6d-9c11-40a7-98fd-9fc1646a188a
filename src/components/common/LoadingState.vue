<template>
  <div class="loading-state" :class="{ 'mobile-loading': isMobile }">
    <!-- Overlay Loading -->
    <div v-if="type === 'overlay'" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner">
          <el-icon class="rotating" :size="size">
            <Loading />
          </el-icon>
        </div>
        <div v-if="message" class="loading-message">{{ message }}</div>
        <div v-if="progress !== null" class="loading-progress">
          <el-progress 
            :percentage="progress" 
            :show-text="false"
            :stroke-width="4"
            color="#e7ab3c"
          />
          <span class="progress-text">{{ progress }}%</span>
        </div>
      </div>
    </div>

    <!-- Inline Loading -->
    <div v-else-if="type === 'inline'" class="loading-inline">
      <el-icon class="rotating loading-icon" :size="size">
        <Loading />
      </el-icon>
      <span v-if="message" class="loading-text">{{ message }}</span>
    </div>

    <!-- Skeleton Loading -->
    <SkeletonLoader 
      v-else-if="type === 'skeleton'"
      :type="skeletonType"
      :rows="skeletonRows"
      :columns="skeletonColumns"
      :fields="skeletonFields"
      :count="skeletonCount"
    />

    <!-- Card Loading -->
    <div v-else-if="type === 'card'" class="loading-card">
      <div class="loading-card-content">
        <div class="loading-spinner">
          <el-icon class="rotating" :size="size">
            <Loading />
          </el-icon>
        </div>
        <div class="loading-info">
          <div v-if="message" class="loading-message">{{ message }}</div>
          <div v-if="submessage" class="loading-submessage">{{ submessage }}</div>
        </div>
      </div>
    </div>

    <!-- Button Loading -->
    <div v-else-if="type === 'button'" class="loading-button">
      <el-button 
        :loading="true"
        :type="buttonType"
        :size="buttonSize"
        disabled
      >
        {{ message || 'Đang xử lý...' }}
      </el-button>
    </div>

    <!-- Dots Loading -->
    <div v-else-if="type === 'dots'" class="loading-dots">
      <div class="dots-container">
        <div class="dot" v-for="i in 3" :key="i"></div>
      </div>
      <div v-if="message" class="loading-message">{{ message }}</div>
    </div>

    <!-- Pulse Loading -->
    <div v-else-if="type === 'pulse'" class="loading-pulse">
      <div class="pulse-circle"></div>
      <div v-if="message" class="loading-message">{{ message }}</div>
    </div>

    <!-- Custom Loading -->
    <div v-else class="loading-custom">
      <slot>
        <div class="loading-spinner">
          <el-icon class="rotating" :size="size">
            <Loading />
          </el-icon>
        </div>
        <div v-if="message" class="loading-message">{{ message }}</div>
      </slot>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import SkeletonLoader from './SkeletonLoader.vue'

// Props
const props = defineProps({
  type: {
    type: String,
    default: 'overlay',
    validator: (value) => [
      'overlay', 'inline', 'skeleton', 'card', 'button', 'dots', 'pulse', 'custom'
    ].includes(value)
  },
  message: {
    type: String,
    default: ''
  },
  submessage: {
    type: String,
    default: ''
  },
  size: {
    type: Number,
    default: 32
  },
  progress: {
    type: Number,
    default: null
  },
  // Skeleton props
  skeletonType: {
    type: String,
    default: 'table'
  },
  skeletonRows: {
    type: Number,
    default: 5
  },
  skeletonColumns: {
    type: Number,
    default: 5
  },
  skeletonFields: {
    type: Number,
    default: 4
  },
  skeletonCount: {
    type: Number,
    default: 4
  },
  // Button props
  buttonType: {
    type: String,
    default: 'primary'
  },
  buttonSize: {
    type: String,
    default: 'default'
  }
})

// Reactive data
const isMobile = ref(false)

// Methods
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768
}

// Lifecycle
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})
</script>

<style scoped>
.loading-state {
  position: relative;
}

.mobile-loading {
  /* Mobile specific adjustments */
}

/* Overlay Loading */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 300px;
  text-align: center;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-message {
  font-size: 16px;
  color: #303133;
  font-weight: 500;
}

.loading-progress {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-text {
  font-size: 14px;
  color: #606266;
  text-align: center;
}

/* Inline Loading */
.loading-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.loading-icon {
  color: #e7ab3c;
}

.loading-text {
  font-size: 14px;
  color: #606266;
}

/* Card Loading */
.loading-card {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.loading-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.loading-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.loading-submessage {
  font-size: 14px;
  color: #909399;
}

/* Button Loading */
.loading-button {
  display: inline-block;
}

/* Dots Loading */
.loading-dots {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.dots-container {
  display: flex;
  gap: 4px;
}

.dot {
  width: 8px;
  height: 8px;
  background: #e7ab3c;
  border-radius: 50%;
  animation: dots-bounce 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0s; }

@keyframes dots-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Pulse Loading */
.loading-pulse {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.pulse-circle {
  width: 40px;
  height: 40px;
  background: #e7ab3c;
  border-radius: 50%;
  animation: pulse-scale 1s infinite ease-in-out;
}

@keyframes pulse-scale {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* Rotating animation */
.rotating {
  animation: rotate 1s linear infinite;
  color: #e7ab3c;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .loading-content {
    padding: 24px;
    max-width: 280px;
  }
  
  .loading-message {
    font-size: 14px;
  }
  
  .loading-card {
    min-height: 150px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .rotating,
  .dot,
  .pulse-circle {
    animation: none;
  }
  
  .loading-icon {
    opacity: 0.7;
  }
}
</style>
