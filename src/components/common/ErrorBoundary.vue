<template>
  <div class="error-boundary">
    <div v-if="hasError" class="error-container" :class="{ 'mobile-error': isMobile }">
      <!-- Error Icon and Title -->
      <div class="error-header">
        <el-icon class="error-icon" :size="isMobile ? 48 : 64">
          <component :is="getErrorIcon()" />
        </el-icon>
        <h2 class="error-title">{{ getErrorTitle() }}</h2>
        <p class="error-message">{{ getErrorMessage() }}</p>
      </div>

      <!-- Error Details (Development only) -->
      <div v-if="showDetails && isDevelopment" class="error-details">
        <el-collapse v-model="activeCollapse">
          <el-collapse-item title="Chi tiết lỗi" name="details">
            <div class="error-stack">
              <pre>{{ errorInfo.stack || errorInfo.message }}</pre>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- Error Actions -->
      <div class="error-actions">
        <el-button 
          type="primary" 
          @click="handleRetry"
          :loading="retrying"
          :size="isMobile ? 'default' : 'large'"
        >
          <el-icon><Refresh /></el-icon>
          Thử lại
        </el-button>
        
        <el-button 
          @click="handleGoHome"
          :size="isMobile ? 'default' : 'large'"
        >
          <el-icon><House /></el-icon>
          Về trang chủ
        </el-button>
        
        <el-button 
          v-if="isDevelopment"
          type="info" 
          @click="showDetails = !showDetails"
          :size="isMobile ? 'default' : 'large'"
        >
          <el-icon><InfoFilled /></el-icon>
          {{ showDetails ? 'Ẩn' : 'Hiện' }} chi tiết
        </el-button>
      </div>

      <!-- Contact Support -->
      <div class="error-support">
        <p>Nếu lỗi vẫn tiếp tục, vui lòng liên hệ bộ phận hỗ trợ:</p>
        <el-button 
          type="text" 
          @click="handleContactSupport"
          class="support-link"
        >
          <el-icon><Message /></el-icon>
          Báo cáo lỗi
        </el-button>
      </div>
    </div>

    <!-- Normal content -->
    <div v-else>
      <slot />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, onErrorCaptured } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  House,
  InfoFilled,
  Message,
  WarningFilled,
  CircleCloseFilled,
  QuestionFilled
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  fallback: {
    type: String,
    default: 'default'
  },
  onError: {
    type: Function,
    default: null
  },
  showRetry: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['error', 'retry'])

// Router
const router = useRouter()

// Reactive data
const hasError = ref(false)
const errorInfo = ref({})
const retrying = ref(false)
const showDetails = ref(false)
const activeCollapse = ref([])
const isMobile = ref(false)

// Computed
const isDevelopment = process.env.NODE_ENV === 'development'

// Methods
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768
}

const getErrorIcon = () => {
  switch (errorInfo.value.type) {
    case 'network':
      return 'WifiOff'
    case 'permission':
      return 'Lock'
    case 'notfound':
      return 'QuestionFilled'
    default:
      return 'CircleCloseFilled'
  }
}

const getErrorTitle = () => {
  switch (errorInfo.value.type) {
    case 'network':
      return 'Lỗi kết nối mạng'
    case 'permission':
      return 'Không có quyền truy cập'
    case 'notfound':
      return 'Không tìm thấy trang'
    case 'server':
      return 'Lỗi máy chủ'
    default:
      return 'Đã xảy ra lỗi'
  }
}

const getErrorMessage = () => {
  switch (errorInfo.value.type) {
    case 'network':
      return 'Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng và thử lại.'
    case 'permission':
      return 'Bạn không có quyền truy cập vào tính năng này. Vui lòng liên hệ quản trị viên.'
    case 'notfound':
      return 'Trang bạn đang tìm kiếm không tồn tại hoặc đã bị di chuyển.'
    case 'server':
      return 'Máy chủ đang gặp sự cố. Chúng tôi đang khắc phục vấn đề.'
    default:
      return errorInfo.value.message || 'Đã xảy ra lỗi không mong muốn. Vui lòng thử lại sau.'
  }
}

const handleError = (error, instance, info) => {
  hasError.value = true
  
  // Determine error type
  let errorType = 'unknown'
  if (error.message?.includes('Network Error') || error.code === 'NETWORK_ERROR') {
    errorType = 'network'
  } else if (error.status === 403 || error.message?.includes('permission')) {
    errorType = 'permission'
  } else if (error.status === 404) {
    errorType = 'notfound'
  } else if (error.status >= 500) {
    errorType = 'server'
  }
  
  errorInfo.value = {
    type: errorType,
    message: error.message,
    stack: error.stack,
    status: error.status,
    timestamp: new Date().toISOString()
  }
  
  // Call custom error handler
  if (props.onError) {
    props.onError(error, instance, info)
  }
  
  // Emit error event
  emit('error', { error, instance, info })
  
  // Log error in development
  if (isDevelopment) {
    console.error('Error caught by ErrorBoundary:', error)
    console.error('Component info:', info)
  }
  
  // Report error to monitoring service (in production)
  if (!isDevelopment) {
    reportError(error, info)
  }
}

const handleRetry = async () => {
  retrying.value = true
  
  try {
    // Wait a bit before retrying
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Reset error state
    hasError.value = false
    errorInfo.value = {}
    
    // Emit retry event
    emit('retry')
    
    ElMessage.success('Đã làm mới thành công')
  } catch (error) {
    ElMessage.error('Không thể làm mới. Vui lòng thử lại.')
  } finally {
    retrying.value = false
  }
}

const handleGoHome = () => {
  router.push('/dashboard')
}

const handleContactSupport = () => {
  // Create error report
  const errorReport = {
    timestamp: errorInfo.value.timestamp,
    type: errorInfo.value.type,
    message: errorInfo.value.message,
    userAgent: navigator.userAgent,
    url: window.location.href
  }
  
  // Copy to clipboard
  navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2))
    .then(() => {
      ElMessage.success('Thông tin lỗi đã được sao chép. Vui lòng gửi cho bộ phận hỗ trợ.')
    })
    .catch(() => {
      ElMessage.info('Vui lòng chụp màn hình và gửi cho bộ phận hỗ trợ.')
    })
}

const reportError = (error, info) => {
  // Send error to monitoring service
  // This would typically be Sentry, LogRocket, etc.
  try {
    // Example: window.Sentry?.captureException(error, { extra: info })
    console.log('Error reported to monitoring service')
  } catch (reportingError) {
    console.error('Failed to report error:', reportingError)
  }
}

// Error capture
onErrorCaptured((error, instance, info) => {
  handleError(error, instance, info)
  return false // Prevent error from propagating
})

// Global error handler
const handleGlobalError = (event) => {
  handleError(event.error, null, 'Global error')
}

const handleUnhandledRejection = (event) => {
  handleError(event.reason, null, 'Unhandled promise rejection')
}

// Lifecycle
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
  window.addEventListener('error', handleGlobalError)
  window.addEventListener('unhandledrejection', handleUnhandledRejection)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
  window.removeEventListener('error', handleGlobalError)
  window.removeEventListener('unhandledrejection', handleUnhandledRejection)
})
</script>

<style scoped>
.error-boundary {
  width: 100%;
  min-height: 200px;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  min-height: 400px;
}

.mobile-error {
  padding: 20px 16px;
  min-height: 300px;
}

.error-header {
  margin-bottom: 32px;
}

.error-icon {
  color: #f56c6c;
  margin-bottom: 16px;
}

.error-title {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.error-message {
  margin: 0 0 24px 0;
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
  max-width: 500px;
}

.error-details {
  width: 100%;
  max-width: 600px;
  margin-bottom: 24px;
  text-align: left;
}

.error-stack {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #606266;
  overflow-x: auto;
}

.error-stack pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.error-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
  justify-content: center;
}

.error-support {
  color: #909399;
  font-size: 14px;
}

.error-support p {
  margin: 0 0 8px 0;
}

.support-link {
  color: #409eff;
  text-decoration: none;
}

.support-link:hover {
  text-decoration: underline;
}

/* Mobile styles */
@media (max-width: 768px) {
  .error-title {
    font-size: 20px;
  }
  
  .error-message {
    font-size: 14px;
  }
  
  .error-actions {
    flex-direction: column;
    width: 100%;
    max-width: 300px;
  }
  
  .error-actions .el-button {
    width: 100%;
  }
}
</style>
