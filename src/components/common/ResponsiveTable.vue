<template>
  <div class="responsive-table-container">
    <!-- Desktop/Tablet Table -->
    <el-table
      v-if="!isMobile"
      v-bind="$attrs"
      :data="data"
      class="responsive-table"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
    >
      <slot />
    </el-table>

    <!-- Mobile Card View -->
    <div v-else class="mobile-cards">
      <!-- Search and Filter for Mobile -->
      <div v-if="showMobileSearch" class="mobile-search-bar">
        <el-input
          v-model="mobileSearchQuery"
          placeholder="Tìm kiếm..."
          :prefix-icon="Search"
          clearable
          @input="handleMobileSearch"
        />
      </div>

      <!-- Cards -->
      <div class="cards-container">
        <div
          v-for="(item, index) in data"
          :key="getItemKey(item, index)"
          class="mobile-card"
          @click="handleCardClick(item, index)"
        >
          <!-- Card Header -->
          <div class="card-header">
            <div class="card-title">
              <slot name="mobile-title" :item="item" :index="index">
                {{ getCardTitle(item) }}
              </slot>
            </div>
            <div class="card-actions">
              <slot name="mobile-actions" :item="item" :index="index">
                <el-dropdown @command="(command) => handleCardAction(command, item)">
                  <el-button type="text" class="card-menu-btn">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit">
                        <el-icon><Edit /></el-icon>
                        Sửa
                      </el-dropdown-item>
                      <el-dropdown-item command="delete" divided>
                        <el-icon><Delete /></el-icon>
                        Xóa
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </slot>
            </div>
          </div>

          <!-- Card Content -->
          <div class="card-content">
            <slot name="mobile-content" :item="item" :index="index">
              <div
                v-for="field in visibleFields"
                :key="field.key"
                class="card-field"
              >
                <span class="field-label">{{ field.label }}:</span>
                <span class="field-value">
                  <slot :name="`mobile-${field.key}`" :item="item" :value="item[field.key]">
                    {{ formatFieldValue(item[field.key], field.type) }}
                  </slot>
                </span>
              </div>
            </slot>
          </div>

          <!-- Card Footer -->
          <div v-if="$slots['mobile-footer']" class="card-footer">
            <slot name="mobile-footer" :item="item" :index="index" />
          </div>
        </div>
      </div>

      <!-- Mobile Pagination -->
      <div v-if="showPagination" class="mobile-pagination">
        <el-pagination
          v-bind="paginationProps"
          layout="prev, pager, next"
          small
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { 
  Search, 
  MoreFilled, 
  Edit, 
  Delete 
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  fields: {
    type: Array,
    default: () => []
  },
  titleField: {
    type: String,
    default: 'name'
  },
  keyField: {
    type: String,
    default: 'id'
  },
  showMobileSearch: {
    type: Boolean,
    default: true
  },
  showPagination: {
    type: Boolean,
    default: true
  },
  paginationProps: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits([
  'sort-change',
  'selection-change',
  'card-click',
  'card-action',
  'mobile-search',
  'current-change',
  'size-change'
])

// Reactive data
const isMobile = ref(false)
const mobileSearchQuery = ref('')

// Methods
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768
}

const getItemKey = (item, index) => {
  return item[props.keyField] || index
}

const getCardTitle = (item) => {
  return item[props.titleField] || `Item ${item[props.keyField] || ''}`
}

const visibleFields = computed(() => {
  return props.fields.filter(field => !field.hideOnMobile)
})

const formatFieldValue = (value, type) => {
  if (value === null || value === undefined) return '-'
  
  switch (type) {
    case 'currency':
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(value)
    case 'number':
      return new Intl.NumberFormat('vi-VN').format(value)
    case 'date':
      return new Date(value).toLocaleDateString('vi-VN')
    case 'datetime':
      return new Date(value).toLocaleString('vi-VN')
    default:
      return value
  }
}

const handleSortChange = (sortInfo) => {
  emit('sort-change', sortInfo)
}

const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

const handleCardClick = (item, index) => {
  emit('card-click', { item, index })
}

const handleCardAction = (command, item) => {
  emit('card-action', { command, item })
}

const handleMobileSearch = () => {
  emit('mobile-search', mobileSearchQuery.value)
}

const handleCurrentChange = (page) => {
  emit('current-change', page)
}

const handleSizeChange = (size) => {
  emit('size-change', size)
}

// Lifecycle
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})
</script>

<style scoped>
.responsive-table-container {
  width: 100%;
}

.responsive-table {
  width: 100%;
}

.mobile-cards {
  width: 100%;
}

.mobile-search-bar {
  margin-bottom: 16px;
}

.cards-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mobile-card {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  cursor: pointer;
}

.mobile-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.mobile-card:active {
  transform: translateY(0);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  flex: 1;
  line-height: 1.4;
}

.card-actions {
  margin-left: 12px;
}

.card-menu-btn {
  padding: 4px;
  color: #909399;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 24px;
}

.field-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  flex-shrink: 0;
  margin-right: 12px;
}

.field-value {
  font-size: 14px;
  color: #303133;
  text-align: right;
  flex: 1;
  word-break: break-word;
}

.card-footer {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.mobile-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1024px) {
  .responsive-table {
    font-size: 14px;
  }
  
  .responsive-table :deep(.el-table__cell) {
    padding: 8px 4px;
  }
}

/* Mobile specific styles */
@media (max-width: 767px) {
  .mobile-card {
    padding: 12px;
  }
  
  .card-title {
    font-size: 15px;
  }
  
  .field-label,
  .field-value {
    font-size: 13px;
  }
  
  .card-field {
    min-height: 20px;
  }
}

/* Animation for card interactions */
@keyframes cardPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

.mobile-card.pulse {
  animation: cardPulse 0.3s ease;
}
</style>
