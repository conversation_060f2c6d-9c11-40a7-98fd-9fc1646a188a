<template>
  <div class="skeleton-loader" :class="{ 'mobile-skeleton': isMobile }">
    <!-- Table Skeleton -->
    <div v-if="type === 'table'" class="skeleton-table">
      <div class="skeleton-header">
        <div class="skeleton-search">
          <div class="skeleton-rect" style="width: 200px; height: 32px;"></div>
          <div class="skeleton-rect" style="width: 100px; height: 32px;"></div>
        </div>
      </div>
      
      <div class="skeleton-table-content">
        <div class="skeleton-table-header">
          <div 
            v-for="i in columns" 
            :key="i" 
            class="skeleton-rect skeleton-th"
            :style="{ width: getColumnWidth(i) }"
          ></div>
        </div>
        
        <div 
          v-for="row in rows" 
          :key="row" 
          class="skeleton-table-row"
        >
          <div 
            v-for="col in columns" 
            :key="col" 
            class="skeleton-rect skeleton-td"
            :style="{ width: getColumnWidth(col) }"
          ></div>
        </div>
      </div>
    </div>

    <!-- Card Skeleton -->
    <div v-else-if="type === 'card'" class="skeleton-card">
      <div class="skeleton-card-header">
        <div class="skeleton-rect" style="width: 60%; height: 20px;"></div>
        <div class="skeleton-circle" style="width: 24px; height: 24px;"></div>
      </div>
      <div class="skeleton-card-content">
        <div 
          v-for="i in 4" 
          :key="i" 
          class="skeleton-card-row"
        >
          <div class="skeleton-rect" style="width: 30%; height: 16px;"></div>
          <div class="skeleton-rect" style="width: 40%; height: 16px;"></div>
        </div>
      </div>
    </div>

    <!-- Form Skeleton -->
    <div v-else-if="type === 'form'" class="skeleton-form">
      <div 
        v-for="i in fields" 
        :key="i" 
        class="skeleton-form-item"
      >
        <div class="skeleton-rect skeleton-label" style="width: 120px; height: 16px;"></div>
        <div class="skeleton-rect skeleton-input" style="width: 100%; height: 40px;"></div>
      </div>
      <div class="skeleton-form-actions">
        <div class="skeleton-rect" style="width: 80px; height: 36px;"></div>
        <div class="skeleton-rect" style="width: 100px; height: 36px;"></div>
      </div>
    </div>

    <!-- Chart Skeleton -->
    <div v-else-if="type === 'chart'" class="skeleton-chart">
      <div class="skeleton-chart-header">
        <div class="skeleton-rect" style="width: 150px; height: 20px;"></div>
        <div class="skeleton-rect" style="width: 80px; height: 24px;"></div>
      </div>
      <div class="skeleton-chart-content">
        <div class="skeleton-chart-area">
          <div class="skeleton-chart-bars">
            <div 
              v-for="i in 8" 
              :key="i" 
              class="skeleton-bar"
              :style="{ height: `${Math.random() * 60 + 20}%` }"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Skeleton -->
    <div v-else-if="type === 'stats'" class="skeleton-stats">
      <div 
        v-for="i in count" 
        :key="i" 
        class="skeleton-stat-card"
      >
        <div class="skeleton-stat-icon">
          <div class="skeleton-circle" style="width: 48px; height: 48px;"></div>
        </div>
        <div class="skeleton-stat-content">
          <div class="skeleton-rect" style="width: 80px; height: 24px; margin-bottom: 8px;"></div>
          <div class="skeleton-rect" style="width: 60px; height: 16px;"></div>
        </div>
      </div>
    </div>

    <!-- Custom Skeleton -->
    <div v-else class="skeleton-custom">
      <slot>
        <div class="skeleton-rect" style="width: 100%; height: 200px;"></div>
      </slot>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'

// Props
const props = defineProps({
  type: {
    type: String,
    default: 'custom',
    validator: (value) => ['table', 'card', 'form', 'chart', 'stats', 'custom'].includes(value)
  },
  rows: {
    type: Number,
    default: 5
  },
  columns: {
    type: Number,
    default: 5
  },
  fields: {
    type: Number,
    default: 4
  },
  count: {
    type: Number,
    default: 4
  },
  animated: {
    type: Boolean,
    default: true
  }
})

// Reactive data
const isMobile = ref(false)

// Methods
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768
}

const getColumnWidth = (index) => {
  const widths = ['15%', '25%', '20%', '15%', '25%']
  return widths[index - 1] || '20%'
}

// Lifecycle
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})
</script>

<style scoped>
.skeleton-loader {
  width: 100%;
  padding: 16px;
}

.mobile-skeleton {
  padding: 8px;
}

/* Base skeleton elements */
.skeleton-rect,
.skeleton-circle {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-circle {
  border-radius: 50%;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Table skeleton */
.skeleton-table {
  width: 100%;
}

.skeleton-header {
  margin-bottom: 16px;
}

.skeleton-search {
  display: flex;
  gap: 12px;
  align-items: center;
}

.skeleton-table-content {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.skeleton-table-header {
  display: flex;
  background: #fafafa;
  padding: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.skeleton-th {
  height: 20px;
  margin-right: 12px;
}

.skeleton-table-row {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-table-row:last-child {
  border-bottom: none;
}

.skeleton-td {
  height: 16px;
  margin-right: 12px;
}

/* Card skeleton */
.skeleton-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.skeleton-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.skeleton-card-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.skeleton-card-row:last-child {
  margin-bottom: 0;
}

/* Form skeleton */
.skeleton-form {
  max-width: 600px;
}

.skeleton-form-item {
  margin-bottom: 20px;
}

.skeleton-label {
  margin-bottom: 8px;
}

.skeleton-input {
  border-radius: 6px;
}

.skeleton-form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

/* Chart skeleton */
.skeleton-chart {
  width: 100%;
}

.skeleton-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.skeleton-chart-content {
  height: 300px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 20px;
}

.skeleton-chart-area {
  height: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.skeleton-chart-bars {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  height: 80%;
  width: 80%;
}

.skeleton-bar {
  flex: 1;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 2px;
  animation-delay: calc(var(--i) * 0.1s);
}

/* Stats skeleton */
.skeleton-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.skeleton-stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.skeleton-stat-icon {
  flex-shrink: 0;
}

.skeleton-stat-content {
  flex: 1;
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .skeleton-table-header,
  .skeleton-table-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .skeleton-th,
  .skeleton-td {
    width: 100% !important;
    margin-right: 0;
  }
  
  .skeleton-stats {
    grid-template-columns: 1fr;
  }
  
  .skeleton-search {
    flex-direction: column;
    align-items: stretch;
  }
  
  .skeleton-chart-content {
    height: 250px;
  }
}

/* Disable animation if user prefers reduced motion */
@media (prefers-reduced-motion: reduce) {
  .skeleton-rect,
  .skeleton-circle,
  .skeleton-bar {
    animation: none;
    background: #f0f0f0;
  }
}
</style>
