<template>
  <div 
    ref="chartRef" 
    :class="['base-chart', { 'mobile-chart': isMobile }]"
    :style="{ width: width, height: height }"
  />
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

// Props
const props = defineProps({
  option: {
    type: Object,
    required: true
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '400px'
  },
  theme: {
    type: String,
    default: 'light'
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Refs
const chartRef = ref()

// Reactive data
const chart = ref(null)
const isMobile = ref(false)

// Methods
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768
}

const initChart = async () => {
  if (!chartRef.value) return
  
  try {
    // Dynamic import for better performance
    const echarts = await import('echarts/core')
    const { CanvasRenderer } = await import('echarts/renderers')
    const { 
      <PERSON><PERSON><PERSON>, 
      <PERSON><PERSON><PERSON>, 
      <PERSON><PERSON><PERSON>, 
      <PERSON><PERSON><PERSON><PERSON><PERSON> 
    } = await import('echarts/charts')
    const {
      TitleComponent,
      TooltipComponent,
      GridComponent,
      LegendComponent,
      DataZoomComponent,
      ToolboxComponent
    } = await import('echarts/components')

    // Register components
    echarts.use([
      CanvasRenderer,
      LineChart,
      BarChart,
      PieChart,
      ScatterChart,
      TitleComponent,
      TooltipComponent,
      GridComponent,
      LegendComponent,
      DataZoomComponent,
      ToolboxComponent
    ])

    // Initialize chart
    chart.value = echarts.init(chartRef.value, props.theme)
    
    // Set responsive option
    const responsiveOption = getResponsiveOption(props.option)
    chart.value.setOption(responsiveOption)
    
    // Handle loading
    if (props.loading) {
      chart.value.showLoading()
    }
    
    // Auto resize
    window.addEventListener('resize', handleResize)
    
  } catch (error) {
    console.error('Failed to initialize chart:', error)
    // Fallback to mock chart
    showMockChart()
  }
}

const getResponsiveOption = (option) => {
  const responsive = { ...option }
  
  if (isMobile.value) {
    // Mobile optimizations
    responsive.grid = {
      ...responsive.grid,
      left: '10%',
      right: '10%',
      top: '15%',
      bottom: '15%'
    }
    
    if (responsive.legend) {
      responsive.legend = {
        ...responsive.legend,
        orient: 'horizontal',
        bottom: 0,
        left: 'center',
        itemWidth: 12,
        itemHeight: 8,
        textStyle: {
          fontSize: 10
        }
      }
    }
    
    if (responsive.tooltip) {
      responsive.tooltip = {
        ...responsive.tooltip,
        textStyle: {
          fontSize: 12
        }
      }
    }
    
    // Reduce font sizes for mobile
    if (responsive.xAxis) {
      responsive.xAxis = Array.isArray(responsive.xAxis) 
        ? responsive.xAxis.map(axis => ({
            ...axis,
            axisLabel: { ...axis.axisLabel, fontSize: 10 }
          }))
        : {
            ...responsive.xAxis,
            axisLabel: { ...responsive.xAxis.axisLabel, fontSize: 10 }
          }
    }
    
    if (responsive.yAxis) {
      responsive.yAxis = Array.isArray(responsive.yAxis)
        ? responsive.yAxis.map(axis => ({
            ...axis,
            axisLabel: { ...axis.axisLabel, fontSize: 10 }
          }))
        : {
            ...responsive.yAxis,
            axisLabel: { ...responsive.yAxis.axisLabel, fontSize: 10 }
          }
    }
  }
  
  return responsive
}

const showMockChart = () => {
  // Show a simple mock chart when ECharts fails to load
  if (chartRef.value) {
    chartRef.value.innerHTML = `
      <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #909399;
        background: #f8f9fa;
        border: 1px dashed #ddd;
        border-radius: 6px;
      ">
        <div style="font-size: 48px; margin-bottom: 16px;">📊</div>
        <div style="font-size: 16px; font-weight: 500;">Biểu đồ</div>
        <div style="font-size: 12px; margin-top: 8px;">Đang tải thư viện charts...</div>
      </div>
    `
  }
}

const handleResize = () => {
  if (chart.value) {
    chart.value.resize()
  }
  checkScreenSize()
}

const updateChart = () => {
  if (chart.value) {
    const responsiveOption = getResponsiveOption(props.option)
    chart.value.setOption(responsiveOption, true)
  }
}

const dispose = () => {
  if (chart.value) {
    chart.value.dispose()
    chart.value = null
  }
  window.removeEventListener('resize', handleResize)
}

// Watchers
watch(() => props.option, () => {
  updateChart()
}, { deep: true })

watch(() => props.loading, (loading) => {
  if (chart.value) {
    if (loading) {
      chart.value.showLoading()
    } else {
      chart.value.hideLoading()
    }
  }
})

watch(isMobile, () => {
  nextTick(() => {
    updateChart()
  })
})

// Lifecycle
onMounted(() => {
  checkScreenSize()
  nextTick(() => {
    initChart()
  })
})

onUnmounted(() => {
  dispose()
})

// Expose methods
defineExpose({
  getChart: () => chart.value,
  resize: handleResize,
  dispose
})
</script>

<style scoped>
.base-chart {
  width: 100%;
  transition: all 0.3s ease;
}

.mobile-chart {
  min-height: 300px;
}

/* Ensure chart container has proper dimensions */
.base-chart > div {
  width: 100% !important;
  height: 100% !important;
}
</style>
