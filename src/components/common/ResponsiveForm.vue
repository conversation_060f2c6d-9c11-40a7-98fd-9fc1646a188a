<template>
  <el-form
    v-bind="$attrs"
    :model="model"
    :rules="rules"
    :label-width="computedLabelWidth"
    :label-position="computedLabelPosition"
    :class="['responsive-form', { 'mobile-form': isMobile }]"
    ref="formRef"
  >
    <slot />
  </el-form>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

// Props
const props = defineProps({
  model: {
    type: Object,
    required: true
  },
  rules: {
    type: Object,
    default: () => ({})
  },
  labelWidth: {
    type: String,
    default: '120px'
  },
  labelPosition: {
    type: String,
    default: 'right'
  },
  mobileLabelWidth: {
    type: String,
    default: '100%'
  },
  mobileLabelPosition: {
    type: String,
    default: 'top'
  }
})

// Refs
const formRef = ref()

// Reactive data
const isMobile = ref(false)

// Methods
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768
}

// Computed
const computedLabelWidth = computed(() => {
  return isMobile.value ? props.mobileLabelWidth : props.labelWidth
})

const computedLabelPosition = computed(() => {
  return isMobile.value ? props.mobileLabelPosition : props.labelPosition
})

// Expose form methods
const validate = (...args) => {
  return formRef.value?.validate(...args)
}

const validateField = (...args) => {
  return formRef.value?.validateField(...args)
}

const resetFields = () => {
  formRef.value?.resetFields()
}

const clearValidate = (...args) => {
  formRef.value?.clearValidate(...args)
}

defineExpose({
  validate,
  validateField,
  resetFields,
  clearValidate
})

// Lifecycle
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})
</script>

<style scoped>
.responsive-form {
  width: 100%;
}

/* Mobile form adjustments */
.mobile-form {
  padding: 0;
}

.mobile-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.mobile-form :deep(.el-form-item__label) {
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1.4;
}

.mobile-form :deep(.el-form-item__content) {
  margin-left: 0 !important;
}

.mobile-form :deep(.el-input),
.mobile-form :deep(.el-select),
.mobile-form :deep(.el-date-picker),
.mobile-form :deep(.el-input-number) {
  width: 100%;
}

.mobile-form :deep(.el-input__inner) {
  height: 44px;
  font-size: 16px; /* Prevent zoom on iOS */
}

.mobile-form :deep(.el-textarea__inner) {
  font-size: 16px; /* Prevent zoom on iOS */
  min-height: 80px;
}

.mobile-form :deep(.el-button) {
  height: 44px;
  font-size: 16px;
}

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1024px) {
  .responsive-form :deep(.el-form-item) {
    margin-bottom: 18px;
  }
  
  .responsive-form :deep(.el-input__inner) {
    height: 38px;
  }
  
  .responsive-form :deep(.el-button) {
    height: 38px;
  }
}

/* Desktop styles */
@media (min-width: 1024px) {
  .responsive-form :deep(.el-form-item) {
    margin-bottom: 22px;
  }
}
</style>
