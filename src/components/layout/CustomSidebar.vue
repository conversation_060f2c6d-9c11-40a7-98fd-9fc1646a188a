<template>
  <el-aside :width="sidebarWidth" class="sidebar">
    <div class="sidebar-content">
      <!-- Logo và tên ứng dụng -->
      <div class="sidebar-header">
        <el-image
          v-if="!collapsed"
          style="width: 32px; height: 32px"
          src="/public/product-management.png"
          fit="cover"
        />
        <span v-if="!collapsed" class="app-name">ERP System</span>
      </div>

      <!-- Menu navigation -->
      <el-menu
        :default-active="activeMenu"
        :collapse="collapsed"
        :unique-opened="true"
        router
        class="sidebar-menu"
        background-color="#001529"
        text-color="#fff"
        active-text-color="#e7ab3c"
      >
        <!-- Dashboard -->
        <el-menu-item index="/dashboard">
          <el-icon><House /></el-icon>
          <template #title>Dashboard</template>
        </el-menu-item>

        <!-- Quản lý Nguyên vật liệu -->
        <el-sub-menu index="materials">
          <template #title>
            <el-icon><Box /></el-icon>
            <span><PERSON><PERSON><PERSON><PERSON> v<PERSON>t liệu</span>
          </template>
          <el-menu-item index="/materials">
            <el-icon><List /></el-icon>
            <template #title>Danh sách</template>
          </el-menu-item>
          <el-menu-item index="/materials/inventory">
            <el-icon><Goods /></el-icon>
            <template #title>Tồn kho</template>
          </el-menu-item>
          <el-menu-item index="/materials/movements">
            <el-icon><TrendCharts /></el-icon>
            <template #title>Nhật ký nhập xuất</template>
          </el-menu-item>
        </el-sub-menu>

        <!-- Quản lý Sản phẩm -->
        <el-sub-menu index="products">
          <template #title>
            <el-icon><ShoppingBag /></el-icon>
            <span>Sản phẩm</span>
          </template>
          <el-menu-item index="/products">
            <el-icon><List /></el-icon>
            <template #title>Danh sách</template>
          </el-menu-item>
          <el-menu-item index="/products/inventory">
            <el-icon><Goods /></el-icon>
            <template #title>Tồn kho</template>
          </el-menu-item>
          <el-menu-item index="/products/movements">
            <el-icon><TrendCharts /></el-icon>
            <template #title>Nhật ký nhập xuất</template>
          </el-menu-item>
        </el-sub-menu>

        <!-- Quản lý BOM -->
        <el-menu-item index="/bom">
          <el-icon><Connection /></el-icon>
          <template #title>Định mức nguyên liệu (BOM)</template>
        </el-menu-item>

        <!-- Quản lý Đơn hàng -->
        <el-sub-menu index="orders">
          <template #title>
            <el-icon><Document /></el-icon>
            <span>Đơn hàng</span>
          </template>
          <el-menu-item index="/orders">
            <el-icon><List /></el-icon>
            <template #title>Danh sách đơn hàng</template>
          </el-menu-item>
          <el-menu-item index="/orders/create">
            <el-icon><Plus /></el-icon>
            <template #title>Tạo đơn hàng</template>
          </el-menu-item>
        </el-sub-menu>

        <!-- Báo cáo -->
        <el-sub-menu index="reports">
          <template #title>
            <el-icon><DataAnalysis /></el-icon>
            <span>Báo cáo</span>
          </template>
          <el-menu-item index="/reports/inventory">
            <el-icon><PieChart /></el-icon>
            <template #title>Báo cáo tồn kho</template>
          </el-menu-item>
          <el-menu-item index="/reports/production">
            <el-icon><TrendCharts /></el-icon>
            <template #title>Báo cáo sản xuất</template>
          </el-menu-item>
          <el-menu-item index="/reports/orders">
            <el-icon><DataLine /></el-icon>
            <template #title>Báo cáo đơn hàng</template>
          </el-menu-item>
        </el-sub-menu>

        <!-- Cài đặt -->
        <el-menu-item index="/settings">
          <el-icon><Setting /></el-icon>
          <template #title>Cài đặt hệ thống</template>
        </el-menu-item>
      </el-menu>
    </div>
  </el-aside>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  House,
  Box,
  ShoppingBag,
  Connection,
  Document,
  DataAnalysis,
  Setting,
  List,
  Goods,
  TrendCharts,
  Plus,
  PieChart,
  DataLine
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  }
})

// Router
const route = useRoute()

// Computed
const sidebarWidth = computed(() => {
  return props.collapsed ? '64px' : '240px'
})

const activeMenu = computed(() => {
  return route.path
})
</script>

<style scoped>
.sidebar {
  background-color: #001529;
  transition: width 0.3s;
  overflow: hidden;
}

.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #1f1f1f;
  min-height: 60px;
}

.app-name {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
}

.sidebar-menu {
  flex: 1;
  border: none;
}

.sidebar-menu:not(.el-menu--collapse) {
  width: 240px;
}

/* Custom menu styles */
:deep(.el-menu-item) {
  height: 48px;
  line-height: 48px;
}

:deep(.el-sub-menu .el-sub-menu__title) {
  height: 48px;
  line-height: 48px;
}

:deep(.el-menu-item:hover) {
  background-color: #1890ff !important;
}

:deep(.el-menu-item.is-active) {
  background-color: #e7ab3c !important;
  color: #000 !important;
}

:deep(.el-sub-menu__title:hover) {
  background-color: #1890ff !important;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 60px;
    left: 0;
    height: calc(100vh - 60px);
    z-index: 1000;
  }
}
</style>
