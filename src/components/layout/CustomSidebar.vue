<template>
  <!-- Mobile overlay -->
  <div
    v-if="isMobile && !collapsed"
    class="sidebar-overlay"
    @click="$emit('toggle-sidebar')"
  ></div>

  <el-aside
    :width="sidebarWidth"
    :class="['sidebar', {
      'sidebar-mobile': isMobile,
      'sidebar-collapsed': collapsed,
      'sidebar-visible': isMobile && !collapsed
    }]"
  >
    <div class="sidebar-content">
      <!-- Logo và tên ứng dụng -->
      <div class="sidebar-header">
        <el-image
          style="width: 32px; height: 32px"
          src="/public/product-management.png"
          fit="cover"
        />
        <span v-if="!collapsed || !isMobile" class="app-name">ERP System</span>

        <!-- Close button for mobile -->
        <el-button
          v-if="isMobile"
          type="text"
          class="sidebar-close"
          @click="$emit('toggle-sidebar')"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>

      <!-- Menu navigation -->
      <el-menu
        :default-active="activeMenu"
        :collapse="collapsed"
        :unique-opened="true"
        router
        class="sidebar-menu"
        background-color="#001529"
        text-color="#fff"
        active-text-color="#e7ab3c"
      >
        <!-- Dashboard -->
        <el-menu-item index="/dashboard">
          <el-icon><House /></el-icon>
          <template #title>Dashboard</template>
        </el-menu-item>

        <!-- Quản lý Nguyên vật liệu -->
        <el-sub-menu index="materials">
          <template #title>
            <el-icon><Box /></el-icon>
            <span>Nguyên vật liệu</span>
          </template>
          <el-menu-item index="/materials">
            <el-icon><List /></el-icon>
            <template #title>Danh sách</template>
          </el-menu-item>
          <el-menu-item index="/materials/inventory">
            <el-icon><Goods /></el-icon>
            <template #title>Tồn kho</template>
          </el-menu-item>
          <el-menu-item index="/materials/movements">
            <el-icon><TrendCharts /></el-icon>
            <template #title>Nhật ký nhập xuất</template>
          </el-menu-item>
        </el-sub-menu>

        <!-- Quản lý Sản phẩm -->
        <el-sub-menu index="products">
          <template #title>
            <el-icon><ShoppingBag /></el-icon>
            <span>Sản phẩm</span>
          </template>
          <el-menu-item index="/products">
            <el-icon><List /></el-icon>
            <template #title>Danh sách</template>
          </el-menu-item>
          <el-menu-item index="/products/inventory">
            <el-icon><Goods /></el-icon>
            <template #title>Tồn kho</template>
          </el-menu-item>
          <el-menu-item index="/products/movements">
            <el-icon><TrendCharts /></el-icon>
            <template #title>Nhật ký nhập xuất</template>
          </el-menu-item>
        </el-sub-menu>

        <!-- Quản lý BOM -->
        <el-menu-item index="/bom">
          <el-icon><Connection /></el-icon>
          <template #title>Định mức nguyên liệu (BOM)</template>
        </el-menu-item>

        <!-- Quản lý Đơn hàng -->
        <el-sub-menu index="orders">
          <template #title>
            <el-icon><Document /></el-icon>
            <span>Đơn hàng</span>
          </template>
          <el-menu-item index="/orders">
            <el-icon><List /></el-icon>
            <template #title>Danh sách đơn hàng</template>
          </el-menu-item>
          <el-menu-item index="/orders/create">
            <el-icon><Plus /></el-icon>
            <template #title>Tạo đơn hàng</template>
          </el-menu-item>
        </el-sub-menu>

        <!-- Báo cáo -->
        <el-sub-menu index="reports">
          <template #title>
            <el-icon><DataAnalysis /></el-icon>
            <span>Báo cáo</span>
          </template>
          <el-menu-item index="/reports/inventory">
            <el-icon><PieChart /></el-icon>
            <template #title>Báo cáo tồn kho</template>
          </el-menu-item>
          <el-menu-item index="/reports/production">
            <el-icon><TrendCharts /></el-icon>
            <template #title>Báo cáo sản xuất</template>
          </el-menu-item>
          <el-menu-item index="/reports/orders">
            <el-icon><DataLine /></el-icon>
            <template #title>Báo cáo đơn hàng</template>
          </el-menu-item>
        </el-sub-menu>

        <!-- Cài đặt -->
        <el-menu-item index="/settings">
          <el-icon><Setting /></el-icon>
          <template #title>Cài đặt hệ thống</template>
        </el-menu-item>
      </el-menu>
    </div>
  </el-aside>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import {
  House,
  Box,
  ShoppingBag,
  Connection,
  Document,
  DataAnalysis,
  Setting,
  List,
  Goods,
  TrendCharts,
  Plus,
  PieChart,
  DataLine,
  Close
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['toggle-sidebar'])

// Router
const route = useRoute()

// Reactive data
const isMobile = ref(false)

// Methods
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768
}

// Computed
const sidebarWidth = computed(() => {
  if (isMobile.value) {
    return props.collapsed ? '0px' : '280px'
  }
  return props.collapsed ? '64px' : '240px'
})

const activeMenu = computed(() => {
  return route.path
})

// Lifecycle
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})
</script>

<style scoped>
.sidebar {
  background-color: #001529;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #1f1f1f;
  min-height: 60px;
}

.app-name {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
  flex: 1;
}

.sidebar-close {
  color: #fff;
  padding: 4px;
  min-width: auto;
}

.sidebar-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-menu {
  flex: 1;
  border: none;
  overflow-y: auto;
}

.sidebar-menu:not(.el-menu--collapse) {
  width: 100%;
}

/* Overlay for mobile */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  backdrop-filter: blur(2px);
}

/* Custom menu styles */
:deep(.el-menu-item) {
  height: 48px;
  line-height: 48px;
  transition: all 0.2s ease;
}

:deep(.el-sub-menu .el-sub-menu__title) {
  height: 48px;
  line-height: 48px;
  transition: all 0.2s ease;
}

:deep(.el-menu-item:hover) {
  background-color: #1890ff !important;
  transform: translateX(2px);
}

:deep(.el-menu-item.is-active) {
  background-color: #e7ab3c !important;
  color: #000 !important;
  font-weight: 600;
}

:deep(.el-sub-menu__title:hover) {
  background-color: #1890ff !important;
  transform: translateX(2px);
}

:deep(.el-sub-menu.is-active .el-sub-menu__title) {
  color: #e7ab3c;
}

/* Mobile specific styles */
.sidebar-mobile {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1000;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  transform: translateX(-100%);
}

.sidebar-mobile.sidebar-visible {
  transform: translateX(0);
}

.sidebar-mobile .sidebar-menu {
  padding-top: 8px;
}

/* Tablet styles */
@media (min-width: 768px) and (max-width: 1024px) {
  .sidebar {
    width: 200px !important;
  }

  .sidebar-header {
    padding: 12px;
  }

  .app-name {
    font-size: 14px;
  }

  :deep(.el-menu-item) {
    height: 44px;
    line-height: 44px;
    font-size: 14px;
  }

  :deep(.el-sub-menu .el-sub-menu__title) {
    height: 44px;
    line-height: 44px;
    font-size: 14px;
  }
}

/* Desktop styles */
@media (min-width: 1024px) {
  .sidebar {
    position: relative;
  }

  .sidebar-collapsed {
    width: 64px !important;
  }

  .sidebar-collapsed .sidebar-header {
    justify-content: center;
  }

  .sidebar-collapsed .app-name {
    display: none;
  }
}

/* Smooth scrollbar for sidebar menu */
.sidebar-menu::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: #001529;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background: #1f1f1f;
  border-radius: 2px;
}

.sidebar-menu::-webkit-scrollbar-thumb:hover {
  background: #333;
}
</style>
