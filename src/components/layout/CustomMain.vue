<template>
  <el-container class="main-layout">
    <!-- Sidebar -->
    <CustomSidebar :collapsed="sidebarCollapsed" />

    <!-- Main content area -->
    <el-container class="main-container">
      <!-- Header -->
      <CustomHeader @toggle-sidebar="toggleSidebar" />

      <!-- Content -->
      <el-main class="main-content">
        <div class="content-wrapper">
          <!-- Breadcrumb -->
          <el-breadcrumb class="breadcrumb" separator="/">
            <el-breadcrumb-item
              v-for="item in breadcrumbItems"
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>

          <!-- Page content -->
          <div class="page-content">
            <router-view />
          </div>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import CustomHeader from './CustomHeader.vue'
import CustomSidebar from './CustomSidebar.vue'

// Reactive data
const sidebarCollapsed = ref(false)

// Router
const route = useRoute()

// Computed
const breadcrumbItems = computed(() => {
  const pathArray = route.path.split('/').filter(path => path)
  const breadcrumbs = [{ title: 'Trang chủ', path: '/' }]

  let currentPath = ''
  pathArray.forEach(path => {
    currentPath += `/${path}`

    // Map path to Vietnamese title
    const titleMap = {
      '/materials': 'Nguyên vật liệu',
      '/materials/inventory': 'Tồn kho nguyên liệu',
      '/materials/movements': 'Nhật ký nguyên liệu',
      '/products': 'Sản phẩm',
      '/products/inventory': 'Tồn kho sản phẩm',
      '/products/movements': 'Nhật ký sản phẩm',
      '/bom': 'Định mức nguyên liệu (BOM)',
      '/orders': 'Đơn hàng',
      '/orders/create': 'Tạo đơn hàng',
      '/reports': 'Báo cáo',
      '/reports/inventory': 'Báo cáo tồn kho',
      '/reports/production': 'Báo cáo sản xuất',
      '/reports/orders': 'Báo cáo đơn hàng',
      '/settings': 'Cài đặt hệ thống'
    }

    breadcrumbs.push({
      title: titleMap[currentPath] || path,
      path: currentPath
    })
  })

  return breadcrumbs
})

// Methods
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}
</script>

<style scoped>
.main-layout {
  height: 100vh;
  width: 100vw;
}

.main-container {
  flex: 1;
  overflow: hidden;
}

.main-content {
  padding: 0;
  background-color: #f0f2f5;
  overflow: auto;
}

.content-wrapper {
  padding: 16px 24px;
  min-height: calc(100vh - 60px);
}

.breadcrumb {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.page-content {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  min-height: calc(100vh - 140px);
}

/* Responsive */
@media (max-width: 768px) {
  .content-wrapper {
    padding: 12px 16px;
  }

  .breadcrumb {
    margin-bottom: 12px;
    padding: 8px 12px;
  }
}
</style>
