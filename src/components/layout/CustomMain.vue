<template>
  <el-container class="main-layout">
    <!-- Sidebar -->
    <CustomSidebar
      :collapsed="sidebarCollapsed"
      @toggle-sidebar="toggleSidebar"
    />

    <!-- Main content area -->
    <el-container class="main-container">
      <!-- Header -->
      <CustomHeader @toggle-sidebar="toggleSidebar" />

      <!-- Content -->
      <el-main class="main-content">
        <div class="content-wrapper" :class="{ 'mobile-content': isMobile }">
          <!-- Breadcrumb -->
          <el-breadcrumb
            v-if="!isMobile || breadcrumbItems.length <= 3"
            class="breadcrumb"
            separator="/"
          >
            <el-breadcrumb-item
              v-for="item in breadcrumbItems"
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>

          <!-- Mobile breadcrumb (simplified) -->
          <div v-else class="mobile-breadcrumb">
            <el-button
              type="text"
              @click="$router.go(-1)"
              class="back-button"
            >
              <el-icon><ArrowLeft /></el-icon>
              Quay lại
            </el-button>
            <span class="current-page">{{ currentPageTitle }}</span>
          </div>

          <!-- Page content -->
          <div class="page-content" :class="{ 'mobile-page': isMobile }">
            <router-view />
          </div>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import CustomHeader from './CustomHeader.vue'
import CustomSidebar from './CustomSidebar.vue'

// Reactive data
const sidebarCollapsed = ref(false)
const isMobile = ref(false)

// Methods
const checkScreenSize = () => {
  const newIsMobile = window.innerWidth < 768
  if (newIsMobile !== isMobile.value) {
    isMobile.value = newIsMobile
    // Auto collapse sidebar on mobile
    if (newIsMobile) {
      sidebarCollapsed.value = true
    }
  }
}

// Router
const route = useRoute()

// Computed
const breadcrumbItems = computed(() => {
  const pathArray = route.path.split('/').filter(path => path)
  const breadcrumbs = [{ title: 'Trang chủ', path: '/' }]

  let currentPath = ''
  pathArray.forEach(path => {
    currentPath += `/${path}`

    // Map path to Vietnamese title
    const titleMap = {
      '/dashboard': 'Dashboard',
      '/materials': 'Nguyên vật liệu',
      '/materials/inventory': 'Tồn kho nguyên liệu',
      '/materials/movements': 'Nhật ký nguyên liệu',
      '/products': 'Sản phẩm',
      '/products/inventory': 'Tồn kho sản phẩm',
      '/products/movements': 'Nhật ký sản phẩm',
      '/bom': 'Định mức nguyên liệu (BOM)',
      '/orders': 'Đơn hàng',
      '/orders/create': 'Tạo đơn hàng',
      '/reports': 'Báo cáo',
      '/reports/inventory': 'Báo cáo tồn kho',
      '/reports/production': 'Báo cáo sản xuất',
      '/reports/orders': 'Báo cáo đơn hàng',
      '/settings': 'Cài đặt hệ thống'
    }

    breadcrumbs.push({
      title: titleMap[currentPath] || path,
      path: currentPath
    })
  })

  return breadcrumbs
})

const currentPageTitle = computed(() => {
  const items = breadcrumbItems.value
  return items[items.length - 1]?.title || 'Trang hiện tại'
})

// Methods
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// Lifecycle
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})
</script>

<style scoped>
.main-layout {
  height: 100vh;
  width: 100vw;
}

.main-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.main-content {
  padding: 0;
  background-color: #f0f2f5;
  overflow: auto;
  flex: 1;
  scroll-behavior: smooth;
}

.content-wrapper {
  padding: 16px 24px;
  min-height: calc(100vh - 60px);
  max-width: 100%;
}

.mobile-content {
  padding: 12px 16px;
}

.breadcrumb {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.breadcrumb:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.mobile-breadcrumb {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.back-button {
  color: #e7ab3c;
  font-weight: 500;
  padding: 4px 8px;
}

.current-page {
  font-weight: 600;
  color: #303133;
  flex: 1;
  text-align: center;
}

.page-content {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-height: calc(100vh - 140px);
  overflow: hidden;
  transition: all 0.2s ease;
}

.mobile-page {
  border-radius: 6px;
  min-height: calc(100vh - 120px);
}

/* Custom scrollbar */
.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.main-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Tablet styles */
@media (min-width: 768px) and (max-width: 1024px) {
  .content-wrapper {
    padding: 14px 20px;
  }

  .breadcrumb,
  .mobile-breadcrumb {
    padding: 10px 14px;
    margin-bottom: 14px;
  }

  .page-content {
    min-height: calc(100vh - 130px);
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .main-layout {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
  }

  .content-wrapper {
    padding: 8px 12px;
  }

  .breadcrumb,
  .mobile-breadcrumb {
    margin-bottom: 12px;
    padding: 8px 12px;
    border-radius: 6px;
  }

  .page-content {
    min-height: calc(100vh - 100px);
    min-height: calc(100dvh - 100px);
  }

  /* Improve touch targets */
  .back-button {
    min-height: 44px;
    padding: 8px 12px;
  }
}
</style>
