<template>
  <el-header class="header">
    <div class="header-left">
      <el-button
        type="text"
        @click="toggleSidebar"
        class="sidebar-toggle"
        :class="{ 'mobile-toggle': isMobile }"
      >
        <el-icon><Menu /></el-icon>
      </el-button>
      <div class="logo-section" v-if="!isMobile || !showMobileSearch">
        <el-image
          style="width: 35px; height: 35px"
          src="/public/product-management.png"
          fit="cover"
        />
        <h1 class="app-title">{{ isMobile ? 'ERP' : 'Hệ thống ERP Quản lý Sản xuất' }}</h1>
      </div>

      <!-- Mobile search -->
      <div v-if="isMobile && showMobileSearch" class="mobile-search">
        <el-input
          v-model="searchQuery"
          placeholder="Tìm kiếm..."
          :prefix-icon="Search"
          clearable
          @blur="showMobileSearch = false"
        />
      </div>
    </div>

    <div class="header-right">
      <!-- Search button for mobile -->
      <el-button
        v-if="isMobile && !showMobileSearch"
        type="text"
        @click="showMobileSearch = true"
        class="mobile-search-btn"
      >
        <el-icon><Search /></el-icon>
      </el-button>

      <!-- Thông báo -->
      <el-badge :value="notificationCount" class="notification-badge" :class="{ 'mobile-hidden': isMobile && showMobileSearch }">
        <el-button type="text" @click="showNotifications">
          <el-icon><Bell /></el-icon>
        </el-button>
      </el-badge>

      <!-- Thông tin user -->
      <el-dropdown @command="handleUserCommand" :class="{ 'mobile-hidden': isMobile && showMobileSearch }">
        <span class="user-info">
          <el-avatar :size="isMobile ? 28 : 32" :src="userAvatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span v-if="!isMobile" class="username">{{ currentUser?.name || 'Admin' }}</span>
          <el-icon v-if="!isMobile" class="el-icon--right"><arrow-down /></el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              Thông tin cá nhân
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              Cài đặt
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              Đăng xuất
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </el-header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Menu,
  Bell,
  User,
  ArrowDown,
  Setting,
  SwitchButton,
  Search
} from '@element-plus/icons-vue'
import { authService } from '@/services'
import { ElMessage } from 'element-plus'

// Props và emits
const emit = defineEmits(['toggle-sidebar'])

// Reactive data
const notificationCount = ref(3)
const userAvatar = ref('')
const isMobile = ref(false)
const showMobileSearch = ref(false)
const searchQuery = ref('')

// Methods
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768
  if (!isMobile.value) {
    showMobileSearch.value = false
  }
}

// Computed
const currentUser = computed(() => {
  return authService.getCurrentUser()
})

// Lifecycle
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})

// Methods
const toggleSidebar = () => {
  emit('toggle-sidebar')
}

const showNotifications = () => {
  ElMessage.info('Tính năng thông báo đang được phát triển')
}

const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('Tính năng thông tin cá nhân đang được phát triển')
      break
    case 'settings':
      ElMessage.info('Tính năng cài đặt đang được phát triển')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = () => {
  authService.logout()
}
</script>

<style scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  height: 60px;
  position: relative;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.sidebar-toggle {
  font-size: 18px;
  color: #606266;
  padding: 8px;
  transition: all 0.2s ease;
}

.sidebar-toggle:hover {
  background-color: #f5f7fa;
  color: #e7ab3c;
}

.mobile-toggle {
  font-size: 20px;
  padding: 6px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 0;
}

.app-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mobile-search {
  flex: 1;
  max-width: 300px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-search-btn {
  font-size: 18px;
  color: #606266;
  padding: 8px;
}

.notification-badge {
  transition: all 0.2s ease;
}

.mobile-hidden {
  opacity: 0;
  pointer-events: none;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 6px 10px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.user-info:hover {
  background-color: #f5f7fa;
  transform: translateY(-1px);
}

.username {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
}

/* Tablet styles */
@media (min-width: 768px) and (max-width: 1024px) {
  .header {
    padding: 0 16px;
  }

  .app-title {
    font-size: 16px;
  }

  .header-right {
    gap: 12px;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .header {
    padding: 0 12px;
  }

  .header-left {
    gap: 8px;
  }

  .app-title {
    font-size: 16px;
  }

  .header-right {
    gap: 4px;
  }

  .notification-badge {
    margin-right: 0;
  }

  .user-info {
    padding: 4px 6px;
  }
}

/* Animation for mobile search */
.mobile-search {
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
