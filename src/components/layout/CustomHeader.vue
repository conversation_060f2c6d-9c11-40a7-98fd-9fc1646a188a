<template>
  <el-row
    style="width: 100%; justify-content: space-between; padding: 5px 2rem"
  >
    <el-col
      :span="6"
      style="display: flex; align-items: center; gap: 1rem; color: white"
    >
      <el-image
        style="width: 35px; height: 35px"
        src="/public/product-management.png"
        fit="cover"
      />
      <span>LYN</span>
    </el-col>

    <el-col :span="12" class="container-center">
      <el-dropdown placement="bottom-start" style="outline: none">
        <Span class="title">MATERIAL</Span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>The Action 1st</el-dropdown-item>
            <el-dropdown-item>The Action 2st</el-dropdown-item>
            <el-dropdown-item>The Action 3st</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-col>

    <el-col
      :span="6"
      style="display: flex; justify-content: end; align-items: center"
    >
      <el-button type="success" plain>Login</el-button>
    </el-col>
  </el-row>
</template>

<script setup lang="ts"></script>

<style scoped>
.container-head {
  width: 100%;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
  color: white;
}
.title {
  color: white;
  outline: none;
}
.container-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
