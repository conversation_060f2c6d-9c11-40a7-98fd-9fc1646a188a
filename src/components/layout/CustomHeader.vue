<template>
  <el-header class="header">
    <div class="header-left">
      <el-button
        type="text"
        @click="toggleSidebar"
        class="sidebar-toggle"
      >
        <el-icon><Menu /></el-icon>
      </el-button>
      <div class="logo-section">
        <el-image
          style="width: 35px; height: 35px"
          src="/public/product-management.png"
          fit="cover"
        />
        <h1 class="app-title">Hệ thống ERP Quản lý Sản xuất</h1>
      </div>
    </div>

    <div class="header-right">
      <!-- Thông báo -->
      <el-badge :value="notificationCount" class="notification-badge">
        <el-button type="text" @click="showNotifications">
          <el-icon><Bell /></el-icon>
        </el-button>
      </el-badge>

      <!-- Thông tin user -->
      <el-dropdown @command="handleUserCommand">
        <span class="user-info">
          <el-avatar :size="32" :src="userAvatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="username">{{ currentUser?.name || 'Admin' }}</span>
          <el-icon class="el-icon--right"><arrow-down /></el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              Thông tin cá nhân
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              Cài đặt
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              Đăng xuất
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </el-header>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  Menu,
  Bell,
  User,
  ArrowDown,
  Setting,
  SwitchButton
} from '@element-plus/icons-vue'
import { authService } from '@/services'
import { ElMessage } from 'element-plus'

// Props và emits
const emit = defineEmits(['toggle-sidebar'])

// Reactive data
const notificationCount = ref(3)
const userAvatar = ref('')

// Computed
const currentUser = computed(() => {
  return authService.getCurrentUser()
})

// Methods
const toggleSidebar = () => {
  emit('toggle-sidebar')
}

const showNotifications = () => {
  ElMessage.info('Tính năng thông báo đang được phát triển')
}

const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('Tính năng thông tin cá nhân đang được phát triển')
      break
    case 'settings':
      ElMessage.info('Tính năng cài đặt đang được phát triển')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = () => {
  authService.logout()
}
</script>

<style scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  font-size: 18px;
  color: #606266;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification-badge {
  margin-right: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

@media (max-width: 768px) {
  .app-title {
    display: none;
  }

  .username {
    display: none;
  }
}
</style>
