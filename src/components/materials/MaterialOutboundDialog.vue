<template>
  <el-dialog
    v-model="dialogVisible"
    title="Xuất kho nguyên vật liệu"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
    >
      <el-form-item label="Nguyên vật liệu" prop="material_id">
        <el-select
          v-model="formData.material_id"
          placeholder="Chọn nguyên vật liệu"
          style="width: 100%"
          filterable
          remote
          :remote-method="searchMaterials"
          :loading="materialsLoading"
          @change="handleMaterialChange"
        >
          <el-option
            v-for="material in materials"
            :key="material.material_id"
            :label="`${material.material_name} (Tồn: ${material.current_qty} ${material.unit})`"
            :value="material.material_id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="Số lượng xuất" prop="quantity">
        <el-input-number
          v-model="formData.quantity"
          :min="1"
          :max="maxQuantity"
          :precision="2"
          placeholder="Nhập số lượng"
          style="width: 100%"
          controls-position="right"
        />
        <div v-if="selectedMaterial" class="quantity-info">
          Tồn kho hiện tại: {{ selectedMaterial.current_qty }} {{ selectedMaterial.unit }}
        </div>
      </el-form-item>

      <el-form-item label="Ghi chú" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="Nhập ghi chú (tùy chọn)"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">Hủy</el-button>
        <el-button 
          type="warning" 
          @click="handleSubmit"
          :loading="submitting"
        >
          Xuất kho
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { materialService } from '@/services'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Refs
const formRef = ref()

// Reactive data
const submitting = ref(false)
const materialsLoading = ref(false)
const dialogVisible = ref(false)
const materials = ref([])
const selectedMaterial = ref(null)

const formData = reactive({
  material_id: null,
  quantity: 1,
  remark: ''
})

// Computed
const maxQuantity = computed(() => {
  return selectedMaterial.value ? selectedMaterial.value.current_qty : 0
})

// Form validation rules
const formRules = {
  material_id: [
    { required: true, message: 'Vui lòng chọn nguyên vật liệu', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: 'Vui lòng nhập số lượng', trigger: 'blur' },
    { type: 'number', min: 1, message: 'Số lượng phải lớn hơn 0', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (selectedMaterial.value && value > selectedMaterial.value.current_qty) {
          callback(new Error('Số lượng xuất không được vượt quá tồn kho'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// Watchers
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetForm()
    loadMaterials()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// Methods
const resetForm = () => {
  Object.assign(formData, {
    material_id: null,
    quantity: 1,
    remark: ''
  })
  
  selectedMaterial.value = null
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const loadMaterials = async () => {
  try {
    materialsLoading.value = true
    // Load materials with inventory data
    const response = await materialService.getMaterialInventory({ limit: 100 })
    
    if (response.success) {
      // Only show materials that have stock
      materials.value = (response.data.inventory || []).filter(item => item.current_qty > 0)
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách nguyên vật liệu:', error)
  } finally {
    materialsLoading.value = false
  }
}

const searchMaterials = async (query) => {
  if (query) {
    try {
      materialsLoading.value = true
      const response = await materialService.getMaterialInventory({ 
        search: query, 
        limit: 50 
      })
      
      if (response.success) {
        materials.value = (response.data.inventory || []).filter(item => item.current_qty > 0)
      }
    } catch (error) {
      console.error('Lỗi khi tìm kiếm nguyên vật liệu:', error)
    } finally {
      materialsLoading.value = false
    }
  }
}

const handleMaterialChange = (materialId) => {
  selectedMaterial.value = materials.value.find(m => m.material_id === materialId)
  formData.quantity = 1
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  try {
    // Validate form
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    const response = await materialService.recordMaterialOutbound(formData)

    if (response.success) {
      ElMessage.success('Xuất kho thành công')
      emit('success')
      handleClose()
    }
  } catch (error) {
    console.error('Lỗi khi xuất kho:', error)
    ElMessage.error('Không thể xuất kho')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.quantity-info {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>
