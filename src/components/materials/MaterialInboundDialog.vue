<template>
  <el-dialog
    v-model="dialogVisible"
    title="Nhập kho nguyên vật liệu"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
    >
      <el-form-item label="Nguyên vật liệu" prop="material_id">
        <el-select
          v-model="formData.material_id"
          placeholder="Chọn nguyên vật liệu"
          style="width: 100%"
          filterable
          remote
          :remote-method="searchMaterials"
          :loading="materialsLoading"
        >
          <el-option
            v-for="material in materials"
            :key="material.material_id"
            :label="`${material.material_name} (${material.supplier})`"
            :value="material.material_id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="Số lượng nhập" prop="quantity">
        <el-input-number
          v-model="formData.quantity"
          :min="1"
          :precision="2"
          placeholder="Nhập số lượng"
          style="width: 100%"
          controls-position="right"
        />
      </el-form-item>

      <el-form-item label="Ghi chú" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="Nhập ghi chú (tùy chọn)"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">Hủy</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
        >
          Nhập kho
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { materialService } from '@/services'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Refs
const formRef = ref()

// Reactive data
const submitting = ref(false)
const materialsLoading = ref(false)
const dialogVisible = ref(false)
const materials = ref([])

const formData = reactive({
  material_id: null,
  quantity: 1,
  remark: ''
})

// Form validation rules
const formRules = {
  material_id: [
    { required: true, message: 'Vui lòng chọn nguyên vật liệu', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: 'Vui lòng nhập số lượng', trigger: 'blur' },
    { type: 'number', min: 1, message: 'Số lượng phải lớn hơn 0', trigger: 'blur' }
  ]
}

// Watchers
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetForm()
    loadMaterials()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// Methods
const resetForm = () => {
  Object.assign(formData, {
    material_id: null,
    quantity: 1,
    remark: ''
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const loadMaterials = async () => {
  try {
    materialsLoading.value = true
    const response = await materialService.getMaterials({ limit: 100 })
    
    if (response.success) {
      materials.value = response.data.materials || []
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách nguyên vật liệu:', error)
  } finally {
    materialsLoading.value = false
  }
}

const searchMaterials = async (query) => {
  if (query) {
    try {
      materialsLoading.value = true
      const response = await materialService.getMaterials({ 
        search: query, 
        limit: 50 
      })
      
      if (response.success) {
        materials.value = response.data.materials || []
      }
    } catch (error) {
      console.error('Lỗi khi tìm kiếm nguyên vật liệu:', error)
    } finally {
      materialsLoading.value = false
    }
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  try {
    // Validate form
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    const response = await materialService.recordMaterialInbound(formData)

    if (response.success) {
      ElMessage.success('Nhập kho thành công')
      emit('success')
      handleClose()
    }
  } catch (error) {
    console.error('Lỗi khi nhập kho:', error)
    ElMessage.error('Không thể nhập kho')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
