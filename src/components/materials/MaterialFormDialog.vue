<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? 'Chỉnh sửa nguyên vật liệu' : 'Thêm nguyên vật liệu mới'"
    :width="isMobile ? '95%' : '600px'"
    :before-close="handleClose"
    class="responsive-dialog"
  >
    <ResponsiveForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      label-position="left"
    >
      <el-form-item label="Tên nguyên vật liệu" prop="material_name">
        <el-input
          v-model="formData.material_name"
          placeholder="Nhập tên nguyên vật liệu"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="Nhà cung cấp" prop="supplier">
        <el-input
          v-model="formData.supplier"
          placeholder="Nhập tên nhà cung cấp"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="<PERSON><PERSON><PERSON> đơn vị (VNĐ)" prop="unit_cost">
        <el-input-number
          v-model="formData.unit_cost"
          :min="0"
          :precision="0"
          :step="1000"
          placeholder="Nhập giá đơn vị"
          style="width: 100%"
          controls-position="right"
        />
      </el-form-item>

      <el-form-item label="Đơn vị tính" prop="unit">
        <el-select
          v-model="formData.unit"
          placeholder="Chọn đơn vị tính"
          style="width: 100%"
          filterable
          allow-create
        >
          <el-option
            v-for="unit in commonUnits"
            :key="unit"
            :label="unit"
            :value="unit"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="Mô tả" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="Nhập mô tả nguyên vật liệu (tùy chọn)"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </ResponsiveForm>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">Hủy</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
        >
          {{ isEdit ? 'Cập nhật' : 'Thêm mới' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { materialService } from '@/services'
import ResponsiveForm from '@/components/common/ResponsiveForm.vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  material: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Refs
const formRef = ref()

// Reactive data
const submitting = ref(false)
const dialogVisible = ref(false)
const isMobile = ref(window.innerWidth < 768)

const formData = reactive({
  material_name: '',
  supplier: '',
  unit_cost: 0,
  unit: '',
  description: ''
})

// Common units for selection
const commonUnits = [
  'kg', 'g', 'tấn', 'lít', 'ml', 'm3',
  'mét', 'cm', 'mm', 'inch',
  'cái', 'chiếc', 'bộ', 'gói', 'thùng'
]

// Form validation rules
const formRules = {
  material_name: [
    { required: true, message: 'Vui lòng nhập tên nguyên vật liệu', trigger: 'blur' },
    { min: 2, max: 100, message: 'Tên nguyên vật liệu phải từ 2-100 ký tự', trigger: 'blur' }
  ],
  supplier: [
    { required: true, message: 'Vui lòng nhập tên nhà cung cấp', trigger: 'blur' },
    { min: 2, max: 100, message: 'Tên nhà cung cấp phải từ 2-100 ký tự', trigger: 'blur' }
  ],
  unit_cost: [
    { required: true, message: 'Vui lòng nhập giá đơn vị', trigger: 'blur' },
    { type: 'number', min: 0, message: 'Giá đơn vị phải lớn hơn hoặc bằng 0', trigger: 'blur' }
  ],
  unit: [
    { required: true, message: 'Vui lòng chọn đơn vị tính', trigger: 'change' }
  ]
}

// Computed
const isEdit = computed(() => {
  return props.material && props.material.material_id
})

// Watchers
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetForm()
    if (props.material) {
      Object.assign(formData, props.material)
    }
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// Methods
const resetForm = () => {
  Object.assign(formData, {
    material_name: '',
    supplier: '',
    unit_cost: 0,
    unit: '',
    description: ''
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  try {
    // Validate form
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    let response
    if (isEdit.value) {
      // Update existing material
      response = await materialService.updateMaterial(props.material.material_id, formData)
    } else {
      // Create new material
      response = await materialService.createMaterial(formData)
    }

    if (response.success) {
      ElMessage.success(
        isEdit.value 
          ? 'Cập nhật nguyên vật liệu thành công' 
          : 'Thêm nguyên vật liệu thành công'
      )
      emit('success')
      handleClose()
    }
  } catch (error) {
    console.error('Lỗi khi lưu nguyên vật liệu:', error)
    ElMessage.error(
      isEdit.value 
        ? 'Không thể cập nhật nguyên vật liệu' 
        : 'Không thể thêm nguyên vật liệu'
    )
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}
</style>
