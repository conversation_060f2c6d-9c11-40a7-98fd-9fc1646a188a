<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? 'Chỉnh sửa BOM' : 'Thêm BOM mới'"
    width="700px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      label-position="left"
    >
      <el-form-item label="Sản phẩm" prop="product_id">
        <el-select
          v-model="formData.product_id"
          placeholder="Chọn sản phẩm"
          style="width: 100%"
          filterable
          remote
          :remote-method="searchProducts"
          :loading="productsLoading"
          @change="handleProductChange"
        >
          <el-option
            v-for="product in products"
            :key="product.product_id"
            :value="product.product_id"
          >
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>{{ product.product_name }}</span>
              <el-tag size="small" :color="getColorCode(product.color)">
                {{ product.color }}
              </el-tag>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="Nguyên vật liệu" prop="material_id">
        <el-select
          v-model="formData.material_id"
          placeholder="Chọn nguyên vật liệu"
          style="width: 100%"
          filterable
          remote
          :remote-method="searchMaterials"
          :loading="materialsLoading"
          @change="handleMaterialChange"
        >
          <el-option
            v-for="material in materials"
            :key="material.material_id"
            :value="material.material_id"
          >
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <div>
                <div>{{ material.material_name }}</div>
                <div style="font-size: 12px; color: #909399;">{{ material.supplier }}</div>
              </div>
              <div style="text-align: right; font-size: 12px; color: #909399;">
                {{ formatCurrency(material.unit_cost) }}/{{ material.unit }}
              </div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="Định mức" prop="m_quantity">
        <el-row :gutter="12">
          <el-col :span="16">
            <el-input-number
              v-model="formData.m_quantity"
              :min="0"
              :precision="3"
              :step="0.1"
              placeholder="Nhập định mức"
              style="width: 100%"
              controls-position="right"
            />
          </el-col>
          <el-col :span="8">
            <el-input
              :value="selectedMaterial?.unit || ''"
              placeholder="Đơn vị"
              disabled
            />
          </el-col>
        </el-row>
      </el-form-item>

      <!-- Thông tin chi phí -->
      <el-form-item v-if="selectedMaterial && formData.m_quantity > 0" label="Chi phí nguyên liệu">
        <div class="cost-info">
          <div class="cost-calculation">
            {{ formData.m_quantity }} {{ selectedMaterial.unit }} × 
            {{ formatCurrency(selectedMaterial.unit_cost) }} = 
            <strong>{{ formatCurrency(formData.m_quantity * selectedMaterial.unit_cost) }}</strong>
          </div>
        </div>
      </el-form-item>

      <!-- Thông tin sản phẩm đã chọn -->
      <el-form-item v-if="selectedProduct" label="Thông tin sản phẩm">
        <div class="product-info">
          <div><strong>{{ selectedProduct.product_name }}</strong></div>
          <div class="product-details">
            <el-tag size="small" :color="getColorCode(selectedProduct.color)">
              {{ selectedProduct.color }}
            </el-tag>
            <span>{{ selectedProduct.product_weight_g }}g</span>
            <span>{{ selectedProduct.injection_time_s }}s</span>
            <span>{{ formatCurrency(selectedProduct.unit_price) }}</span>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="Ghi chú" prop="notes">
        <el-input
          v-model="formData.notes"
          type="textarea"
          :rows="3"
          placeholder="Nhập ghi chú (tùy chọn)"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">Hủy</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
        >
          {{ isEdit ? 'Cập nhật' : 'Thêm mới' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { bomService, productService, materialService } from '@/services'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  bom: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Refs
const formRef = ref()

// Reactive data
const submitting = ref(false)
const productsLoading = ref(false)
const materialsLoading = ref(false)
const dialogVisible = ref(false)
const products = ref([])
const materials = ref([])
const selectedProduct = ref(null)
const selectedMaterial = ref(null)

const formData = reactive({
  product_id: null,
  material_id: null,
  m_quantity: 0,
  notes: ''
})

// Form validation rules
const formRules = {
  product_id: [
    { required: true, message: 'Vui lòng chọn sản phẩm', trigger: 'change' }
  ],
  material_id: [
    { required: true, message: 'Vui lòng chọn nguyên vật liệu', trigger: 'change' }
  ],
  m_quantity: [
    { required: true, message: 'Vui lòng nhập định mức', trigger: 'blur' },
    { type: 'number', min: 0, message: 'Định mức phải lớn hơn 0', trigger: 'blur' }
  ]
}

// Computed
const isEdit = computed(() => {
  return props.bom && props.bom.bom_id
})

// Watchers
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetForm()
    loadInitialData()
    if (props.bom) {
      Object.assign(formData, props.bom)
      // Load selected product and material info
      if (props.bom.product_id) {
        handleProductChange(props.bom.product_id)
      }
      if (props.bom.material_id) {
        handleMaterialChange(props.bom.material_id)
      }
    }
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// Methods
const resetForm = () => {
  Object.assign(formData, {
    product_id: null,
    material_id: null,
    m_quantity: 0,
    notes: ''
  })
  
  selectedProduct.value = null
  selectedMaterial.value = null
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const loadInitialData = async () => {
  await Promise.all([
    loadProducts(),
    loadMaterials()
  ])
}

const loadProducts = async () => {
  try {
    productsLoading.value = true
    const response = await productService.getProducts({ limit: 100 })
    
    if (response.success) {
      products.value = response.data.products || []
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách sản phẩm:', error)
  } finally {
    productsLoading.value = false
  }
}

const loadMaterials = async () => {
  try {
    materialsLoading.value = true
    const response = await materialService.getMaterials({ limit: 100 })
    
    if (response.success) {
      materials.value = response.data.materials || []
    }
  } catch (error) {
    console.error('Lỗi khi tải danh sách nguyên vật liệu:', error)
  } finally {
    materialsLoading.value = false
  }
}

const searchProducts = async (query) => {
  if (query) {
    try {
      productsLoading.value = true
      const response = await productService.getProducts({ 
        search: query, 
        limit: 50 
      })
      
      if (response.success) {
        products.value = response.data.products || []
      }
    } catch (error) {
      console.error('Lỗi khi tìm kiếm sản phẩm:', error)
    } finally {
      productsLoading.value = false
    }
  }
}

const searchMaterials = async (query) => {
  if (query) {
    try {
      materialsLoading.value = true
      const response = await materialService.getMaterials({ 
        search: query, 
        limit: 50 
      })
      
      if (response.success) {
        materials.value = response.data.materials || []
      }
    } catch (error) {
      console.error('Lỗi khi tìm kiếm nguyên vật liệu:', error)
    } finally {
      materialsLoading.value = false
    }
  }
}

const handleProductChange = (productId) => {
  selectedProduct.value = products.value.find(p => p.product_id === productId)
}

const handleMaterialChange = (materialId) => {
  selectedMaterial.value = materials.value.find(m => m.material_id === materialId)
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  try {
    // Validate form
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    let response
    if (isEdit.value) {
      // Update existing BOM
      response = await bomService.updateBOM(props.bom.bom_id, formData)
    } else {
      // Create new BOM
      response = await bomService.createBOM(formData)
    }

    if (response.success) {
      ElMessage.success(
        isEdit.value 
          ? 'Cập nhật BOM thành công' 
          : 'Thêm BOM thành công'
      )
      emit('success')
      handleClose()
    }
  } catch (error) {
    console.error('Lỗi khi lưu BOM:', error)
    ElMessage.error(
      isEdit.value 
        ? 'Không thể cập nhật BOM' 
        : 'Không thể thêm BOM'
    )
  } finally {
    submitting.value = false
  }
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount)
}

const getColorCode = (colorName) => {
  const colorMap = {
    'Đỏ': '#f56c6c',
    'Xanh': '#409eff',
    'Vàng': '#e6a23c',
    'Xanh lá': '#67c23a',
    'Tím': '#9c27b0',
    'Cam': '#ff9800',
    'Hồng': '#e91e63',
    'Nâu': '#795548',
    'Xám': '#9e9e9e',
    'Đen': '#424242',
    'Trắng': '#ffffff'
  }
  return colorMap[colorName] || '#909399'
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cost-info {
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.cost-calculation {
  font-size: 14px;
  color: #606266;
}

.product-info {
  line-height: 1.4;
}

.product-details {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}
</style>
