# Hệ thống ERP Quản lý Quy trình Sản xuất

## Tổng quan

Hệ thống ERP được phát triển bằng Vue.js 3 và Element Plus, chuyên dụng cho việc quản lý quy trình sản xuất từ nguyên vật liệu đến thành phẩm. <PERSON>ệ thống bao gồm các module chính:

- **Quản lý Nguyên vật liệu**: <PERSON><PERSON><PERSON><PERSON> l<PERSON>nh sách, tồn kho và nhật ký nhập xuất nguyên vật liệu
- **Quản lý Sản phẩm**: <PERSON><PERSON><PERSON><PERSON> lý danh sách, tồn kho và nhật ký nhập xuất sản phẩm
- **Quản lý BOM (Bill of Materials)**: <PERSON><PERSON><PERSON> mức nguyên vật liệu cho từng sản phẩm
- **Quản lý Đơn hàng**: <PERSON><PERSON><PERSON> và theo dõi đơn hà<PERSON>, chi tiết đơn hàng
- **<PERSON><PERSON><PERSON> cáo**: <PERSON><PERSON><PERSON> cáo tồn kho, sản xuất và đơn hàng
- **Cài đặt hệ thống**: Cấu hình các thông số hệ thống

## Công nghệ sử dụng

### Frontend
- **Vue.js 3**: Framework JavaScript chính
- **Element Plus**: Thư viện UI components
- **Vue Router 4**: Quản lý routing
- **Axios**: HTTP client cho API calls
- **Vite**: Build tool và dev server

### Styling
- **SCSS/Sass**: CSS preprocessor
- **Element Plus Theme**: Tùy chỉnh theme với màu chủ đạo #e7ab3c

### Architecture
- **Component-based**: Kiến trúc component tái sử dụng
- **Service Layer**: Tách biệt logic API calls
- **Route Guards**: Bảo mật và phân quyền
- **Responsive Design**: Tương thích mobile và tablet

## Cấu trúc dự án

```
src/
├── components/           # Các component tái sử dụng
│   ├── layout/          # Layout components (Header, Sidebar, Main)
│   ├── materials/       # Components cho module nguyên vật liệu
│   ├── products/        # Components cho module sản phẩm
│   ├── bom/            # Components cho module BOM
│   └── common/         # Common components
├── views/              # Các trang chính
│   ├── materials/      # Trang quản lý nguyên vật liệu
│   ├── products/       # Trang quản lý sản phẩm
│   ├── bom/           # Trang quản lý BOM
│   ├── orders/        # Trang quản lý đơn hàng
│   └── reports/       # Trang báo cáo
├── services/          # API services
├── router/           # Cấu hình routing
├── utils/           # Utility functions
├── locales/         # Đa ngôn ngữ
└── assets/          # Static assets
```

## Cài đặt và chạy dự án

### Yêu cầu hệ thống
- Node.js >= 16.x
- npm >= 8.x hoặc yarn >= 1.22.x

### Cài đặt dependencies
```bash
npm install
# hoặc
yarn install
```

### Chạy development server
```bash
npm run dev
# hoặc
yarn dev
```

Ứng dụng sẽ chạy tại: `http://localhost:5173`

### Build production
```bash
npm run build
# hoặc
yarn build
```

## Hướng dẫn sử dụng

### Đăng nhập
- **URL**: `/login`
- **Demo Account**:
  - Username: `admin`
  - Password: `admin123`

### Dashboard
- Trang chủ hiển thị tổng quan hệ thống
- Thống kê nhanh về nguyên vật liệu, sản phẩm, đơn hàng
- Thao tác nhanh và hoạt động gần đây

### Quản lý Nguyên vật liệu
1. **Danh sách nguyên vật liệu** (`/materials`)
   - Xem danh sách tất cả nguyên vật liệu
   - Thêm, sửa, xóa nguyên vật liệu
   - Tìm kiếm và lọc theo nhà cung cấp

2. **Tồn kho nguyên vật liệu** (`/materials/inventory`)
   - Xem tình trạng tồn kho
   - Nhập kho và xuất kho
   - Cảnh báo nguyên vật liệu sắp hết

3. **Nhật ký nhập xuất** (`/materials/movements`)
   - Theo dõi lịch sử nhập xuất
   - Lọc theo loại giao dịch và thời gian

### Quản lý Sản phẩm
- Tương tự như quản lý nguyên vật liệu
- Bao gồm thông tin trọng lượng, thời gian ép, màu sắc

### Quản lý BOM (Bill of Materials)
- Định nghĩa định mức nguyên vật liệu cho từng sản phẩm
- Tính toán chi phí nguyên vật liệu
- Kiểm tra tồn kho có đủ cho sản xuất

### Báo cáo
- **Báo cáo tồn kho**: Thống kê tình trạng tồn kho
- **Báo cáo sản xuất**: Hiệu suất sản xuất (đang phát triển)
- **Báo cáo đơn hàng**: Tình trạng đơn hàng (đang phát triển)

## API Integration

### Cấu hình API
File: `src/services/api.js`
- Base URL: `http://localhost:3000/api` (có thể thay đổi qua env)
- Timeout: 10 seconds
- Auto retry với exponential backoff

### Authentication
- JWT token được lưu trong localStorage
- Auto refresh token khi hết hạn
- Redirect về login khi unauthorized

### Error Handling
- Global error interceptor
- Hiển thị thông báo lỗi bằng Element Plus notification
- Logging errors trong development mode

## Tính năng nổi bật

### 1. Responsive Design
- Tương thích với desktop, tablet và mobile
- Sidebar collapse trên mobile
- Adaptive table columns

### 2. Real-time Updates
- Auto refresh data
- Optimistic updates
- Loading states

### 3. Advanced Search & Filter
- Debounced search
- Multiple filter criteria
- Sort by multiple columns

### 4. Form Validation
- Real-time validation
- Custom validation rules
- Vietnamese error messages

### 5. Internationalization
- Hỗ trợ tiếng Việt và tiếng Anh
- Dynamic language switching
- Date/number formatting theo locale

## Customization

### Theme Customization
File: `src/utils/constants.js`
```javascript
export const THEME_CONFIG = {
  PRIMARY_COLOR: '#e7ab3c',
  SUCCESS_COLOR: '#67c23a',
  WARNING_COLOR: '#e6a23c',
  DANGER_COLOR: '#f56c6c',
  INFO_COLOR: '#409eff'
}
```

### Adding New Modules
1. Tạo service trong `src/services/`
2. Tạo components trong `src/components/`
3. Tạo views trong `src/views/`
4. Thêm routes trong `src/router/`
5. Cập nhật navigation trong `CustomSidebar.vue`

## Deployment

### Docker
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "preview"]
```

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://backend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Troubleshooting

### Common Issues

1. **Module not found errors**
   - Kiểm tra đường dẫn import
   - Đảm bảo file tồn tại
   - Restart dev server

2. **API connection errors**
   - Kiểm tra backend server đang chạy
   - Verify API endpoints
   - Check CORS configuration

3. **Build errors**
   - Clear node_modules và reinstall
   - Check for TypeScript errors
   - Verify all imports

## Contributing

1. Fork repository
2. Tạo feature branch
3. Commit changes với message rõ ràng
4. Push và tạo Pull Request
5. Code review và merge

## License

MIT License - Xem file LICENSE để biết thêm chi tiết.

## Support

Để được hỗ trợ, vui lòng:
1. Kiểm tra documentation
2. Search trong issues
3. Tạo issue mới với thông tin chi tiết
4. Liên hệ team development

---

**Phát triển bởi**: Team ERP Development  
**Phiên bản**: 1.0.0  
**Cập nhật cuối**: 17/06/2025
