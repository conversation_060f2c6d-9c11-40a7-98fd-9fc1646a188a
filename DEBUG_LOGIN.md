# Debug Đăng nhập Demo

## 🔍 Kiểm tra lỗi đăng nhập

### 1. Mở Developer Tools
- Nhấn F12 hoặc Ctrl+Shift+I
- Vào tab Console để xem lỗi

### 2. Thông tin đăng nhập demo
- **Username**: `admin`
- **Password**: `admin123`

### 3. <PERSON><PERSON><PERSON> bước debug

#### Bước 1: Kiểm tra Console
Xem có lỗi JavaScript nào không:
```
- Module import errors
- Component loading errors
- Router errors
```

#### Bước 2: Kiểm tra Network tab
Xem có request nào fail không:
```
- 404 errors cho components
- API call errors
```

#### Bước 3: Kiểm tra localStorage
Sau khi đăng nhập, kiểm tra:
```javascript
// Mở Console và chạy:
console.log('Token:', localStorage.getItem('auth_token'))
console.log('User:', localStorage.getItem('user_info'))
```

### 4. Lỗi thường gặp và cách sửa

#### Lỗi 1: <PERSON><PERSON>le not found
```
Cannot resolve module '@/views/...'
```
**Giải pháp**: Restart dev server
```bash
npm run dev
```

#### Lỗi 2: Component loading failed
```
Failed to load component
```
**Giải pháp**: Kiểm tra file tồn tại và đường dẫn đúng

#### Lỗi 3: Router guard errors
```
Navigation cancelled
```
**Giải pháp**: Đã sửa trong tokenGuard để handle mock token

### 5. Test thủ công

#### Test 1: Đăng nhập thành công
1. Nhập `admin` / `admin123`
2. Nhấn "Đăng nhập"
3. Kiểm tra redirect về `/dashboard`

#### Test 2: Đăng nhập sai
1. Nhập sai username/password
2. Kiểm tra hiển thị lỗi

#### Test 3: Navigation sau đăng nhập
1. Đăng nhập thành công
2. Click các menu item
3. Kiểm tra trang load đúng

### 6. Fallback solution

Nếu vẫn không được, thử bypass authentication:

#### Cách 1: Set token thủ công
```javascript
// Mở Console và chạy:
localStorage.setItem('auth_token', 'mock-jwt-token-' + Date.now())
localStorage.setItem('user_info', JSON.stringify({
  id: 1,
  username: 'admin',
  name: 'Administrator',
  email: '<EMAIL>',
  role: 'admin',
  permissions: ['all']
}))
// Sau đó refresh trang
location.reload()
```

#### Cách 2: Tắt authentication tạm thời
Trong file `src/router/guards.js`, comment dòng:
```javascript
// router.beforeEach(authGuard)
```

### 7. Kiểm tra cấu hình Vite

Đảm bảo file `vite.config.js` có alias:
```javascript
export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  }
})
```

### 8. Restart hoàn toàn

Nếu tất cả đều fail:
```bash
# Dừng dev server (Ctrl+C)
# Xóa cache
rm -rf node_modules/.vite
# Restart
npm run dev
```

### 9. Kiểm tra browser compatibility

Đảm bảo browser hỗ trợ:
- ES6+ features
- Local Storage
- Modern JavaScript

### 10. Alternative access

Nếu login không hoạt động, truy cập trực tiếp:
```
http://localhost:5173/dashboard
```

Sau đó set token thủ công như ở Cách 1.

---

**Nếu vẫn gặp vấn đề, vui lòng share:**
1. Console errors
2. Network tab errors  
3. Browser và version
4. Các bước đã thử
